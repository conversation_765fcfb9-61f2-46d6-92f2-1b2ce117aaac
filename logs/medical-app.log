2025-08-30 17:21:58 [main] INFO  c.medicalapp.MedicalAppApplication - Starting MedicalAppApplication using Java 23.0.2 with PID 14372 (G:\Projects\docfile\backend\target\classes started by DELL in G:\Projects\docfile)
2025-08-30 17:21:58 [main] DEBUG c.medicalapp.MedicalAppApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-30 17:21:58 [main] INFO  c.medicalapp.MedicalAppApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-30 17:21:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-30 17:21:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 109 ms. Found 7 MongoDB repository interfaces.
2025-08-30 17:22:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tom<PERSON> initialized with port 8080 (http)
2025-08-30 17:22:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 17:22:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-30 17:22:00 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-30 17:22:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2108 ms
2025-08-30 17:22:00 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.11.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/23.0.2+7-58"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='madhawa', source='medical_data_db', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, streamFactoryFactory=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@4703c998], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@71166348, com.mongodb.Jep395RecordCodecProvider@6d874695, com.mongodb.KotlinCodecProvider@20bb85b4]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@79add732], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-08-30 17:22:00 [cluster-ClusterId{value='68b2e5e0e95e600ef7e8a4c4', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=29414400, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000058, setVersion=1, topologyVersion=TopologyVersion{processId=68b278112d916cba60280c78, counter=6}, lastWriteDate=Sat Aug 30 17:22:00 IST 2025, lastUpdateTimeNanos=28141761467900}
2025-08-30 17:22:01 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-30 17:22:01 [main] DEBUG c.m.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-08-30 17:22:02 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 14 mappings in 'requestMappingHandlerMapping'
2025-08-30 17:22:02 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-08-30 17:22:02 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-08-30 17:22:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2a2798a2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2b9ecd05, org.springframework.security.web.context.SecurityContextHolderFilter@72e75786, org.springframework.security.web.header.HeaderWriterFilter@38cf4497, org.springframework.web.filter.CorsFilter@31f1b268, org.springframework.security.web.authentication.logout.LogoutFilter@26cdd4af, com.medicalapp.security.AuthTokenFilter@7433ca19, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6fbc7853, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@733fa95c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3ec7ad61, org.springframework.security.web.session.SessionManagementFilter@57b130f6, org.springframework.security.web.access.ExceptionTranslationFilter@43d6262c, org.springframework.security.web.access.intercept.AuthorizationFilter@60bc308b]
2025-08-30 17:22:02 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-30 17:22:02 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-30 17:22:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-30 17:22:03 [main] INFO  c.medicalapp.MedicalAppApplication - Started MedicalAppApplication in 5.757 seconds (process running for 8.778)
2025-08-30 17:22:04 [RMI TCP Connection(2)-**********] WARN  o.s.b.a.d.mongo.MongoHealthIndicator - MongoDB health check failed
org.springframework.data.mongodb.UncategorizedMongoDbException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-1, userName='madhawa', source='medical_data_db', password=<hidden>, mechanismProperties=<hidden>}
	at org.springframework.data.mongodb.core.MongoExceptionTranslator.translateExceptionIfPossible(MongoExceptionTranslator.java:135)
	at org.springframework.data.mongodb.core.MongoTemplate.potentiallyConvertRuntimeException(MongoTemplate.java:2993)
	at org.springframework.data.mongodb.core.MongoTemplate.execute(MongoTemplate.java:582)
	at org.springframework.data.mongodb.core.MongoTemplate.executeCommand(MongoTemplate.java:514)
	at org.springframework.boot.actuate.data.mongo.MongoHealthIndicator.doHealthCheck(MongoHealthIndicator.java:46)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:82)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:76)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:803)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1520)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1348)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1435)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:841)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-1, userName='madhawa', source='medical_data_db', password=<hidden>, mechanismProperties=<hidden>}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:270)
	at com.mongodb.internal.connection.SaslAuthenticator.getNextSaslResponse(SaslAuthenticator.java:133)
	at com.mongodb.internal.connection.SaslAuthenticator.lambda$authenticate$0(SaslAuthenticator.java:63)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:277)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:59)
	at com.mongodb.internal.connection.DefaultAuthenticator.authenticate(DefaultAuthenticator.java:57)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticate(InternalStreamConnectionInitializer.java:206)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.finishHandshake(InternalStreamConnectionInitializer.java:86)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:216)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:55)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:647)
	at com.mongodb.internal.connection.DefaultConnectionPool$OpenConcurrencyLimiter.openWithConcurrencyLimit(DefaultConnectionPool.java:993)
	at com.mongodb.internal.connection.DefaultConnectionPool$OpenConcurrencyLimiter.openOrGetAvailable(DefaultConnectionPool.java:934)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:203)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:192)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:96)
	at com.mongodb.internal.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:186)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:196)
	at com.mongodb.internal.operation.SyncOperationHelper.withSuppliedResource(SyncOperationHelper.java:144)
	at com.mongodb.internal.operation.SyncOperationHelper.lambda$withSourceAndConnection$1(SyncOperationHelper.java:126)
	at com.mongodb.internal.operation.SyncOperationHelper.withSuppliedResource(SyncOperationHelper.java:152)
	at com.mongodb.internal.operation.SyncOperationHelper.withSourceAndConnection(SyncOperationHelper.java:125)
	at com.mongodb.internal.operation.SyncOperationHelper.lambda$executeRetryableRead$4(SyncOperationHelper.java:189)
	at com.mongodb.internal.operation.SyncOperationHelper.lambda$decorateReadWithRetries$12(SyncOperationHelper.java:292)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.SyncOperationHelper.executeRetryableRead(SyncOperationHelper.java:194)
	at com.mongodb.internal.operation.SyncOperationHelper.executeRetryableRead(SyncOperationHelper.java:176)
	at com.mongodb.internal.operation.CommandReadOperation.execute(CommandReadOperation.java:48)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:153)
	at com.mongodb.client.internal.MongoDatabaseImpl.executeCommand(MongoDatabaseImpl.java:196)
	at com.mongodb.client.internal.MongoDatabaseImpl.runCommand(MongoDatabaseImpl.java:165)
	at com.mongodb.client.internal.MongoDatabaseImpl.runCommand(MongoDatabaseImpl.java:160)
	at org.springframework.data.mongodb.core.MongoTemplate.lambda$executeCommand$3(MongoTemplate.java:514)
	at org.springframework.data.mongodb.core.MongoTemplate.execute(MongoTemplate.java:580)
	... 42 common frames omitted
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is {"ok": 0.0, "errmsg": "Authentication failed.", "code": 18, "codeName": "AuthenticationFailed", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1756554720, "i": 1}}, "signature": {"hash": {"$binary": {"base64": "AAAAAAAAAAAAAAAAAAAAAAAAAAA=", "subType": "00"}}, "keyId": 0}}, "operationTime": {"$timestamp": {"t": 1756554720, "i": 1}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:454)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:372)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:102)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:49)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslStart(SaslAuthenticator.java:224)
	at com.mongodb.internal.connection.SaslAuthenticator.getNextSaslResponse(SaslAuthenticator.java:131)
	... 74 common frames omitted
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7488a630
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@69ee3302
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-30 17:22:04 [RMI TCP Connection(3)-**********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-08-30 17:22:14 [cluster-ClusterId{value='68b2e5e0e95e600ef7e8a4c4', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=102313300, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000058, setVersion=1, topologyVersion=TopologyVersion{processId=68b278112d916cba60280c78, counter=6}, lastWriteDate=Sat Aug 30 17:22:10 IST 2025, lastUpdateTimeNanos=28155195667800}
2025-08-30 17:27:54 [main] INFO  c.medicalapp.MedicalAppApplication - Starting MedicalAppApplication using Java 23.0.2 with PID 11524 (G:\Projects\docfile\backend\target\classes started by DELL in G:\Projects\docfile)
2025-08-30 17:27:54 [main] DEBUG c.medicalapp.MedicalAppApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-30 17:27:54 [main] INFO  c.medicalapp.MedicalAppApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-30 17:27:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-30 17:27:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 62 ms. Found 7 MongoDB repository interfaces.
2025-08-30 17:27:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-30 17:27:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 17:27:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-30 17:27:55 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-30 17:27:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1581 ms
2025-08-30 17:27:56 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.11.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/23.0.2+7-58"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='madhawa', source='medical_data_db', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, streamFactoryFactory=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@2bf2d6eb], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@6d6cd1e0, com.mongodb.Jep395RecordCodecProvider@76a9a009, com.mongodb.KotlinCodecProvider@9785903]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@34009349], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-08-30 17:27:56 [cluster-ClusterId{value='68b2e744856a1303af131d10', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=24856400, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000058, setVersion=1, topologyVersion=TopologyVersion{processId=68b278112d916cba60280c78, counter=6}, lastWriteDate=Sat Aug 30 17:27:43 IST 2025, lastUpdateTimeNanos=28497050238100}
2025-08-30 17:27:56 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-30 17:27:56 [main] DEBUG c.m.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-08-30 17:27:57 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 14 mappings in 'requestMappingHandlerMapping'
2025-08-30 17:27:57 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-08-30 17:27:57 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-08-30 17:27:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@362be0cd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2dde0a34, org.springframework.security.web.context.SecurityContextHolderFilter@83e635f, org.springframework.security.web.header.HeaderWriterFilter@51869ad6, org.springframework.web.filter.CorsFilter@61836cd9, org.springframework.security.web.authentication.logout.LogoutFilter@b56c222, com.medicalapp.security.AuthTokenFilter@55053f81, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3b56947a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1926f962, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1b632442, org.springframework.security.web.session.SessionManagementFilter@46591e98, org.springframework.security.web.access.ExceptionTranslationFilter@1eb9d69a, org.springframework.security.web.access.intercept.AuthorizationFilter@647ff23e]
2025-08-30 17:27:57 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-30 17:27:57 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-30 17:27:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-30 17:27:57 [main] INFO  c.medicalapp.MedicalAppApplication - Started MedicalAppApplication in 3.982 seconds (process running for 4.754)
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@647af0ed
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@f23e388
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-30 17:27:58 [RMI TCP Connection(4)-**********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-08-30 17:28:42 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /auth/signin
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/signin
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-30 17:28:42 [http-nio-8080-exec-3] WARN  o.s.w.s.h.HandlerMappingIntrospector - Cache miss for REQUEST dispatch to '/api/auth/signin' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/signin
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/signin", parameters={}
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.AuthController#authenticateUser(LoginRequest)
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [com.medicalapp.dto.request.LoginRequest@5f96822b]
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.dto.response.JwtResponse@5ec2c03]
2025-08-30 17:28:42 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@4ccbd011]
2025-08-30 17:28:43 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 17:29:00 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/medications/current
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/medications/current
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/medications/current
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/medications/current", parameters={}
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentMedications(Authentication)
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentMedications(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentMedications(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [[]]
2025-08-30 17:29:01 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 17:29:02 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/history
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/history
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/history
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/history", parameters={}
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getMedicalHistory(Authentication)
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getMedicalHistory(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getMedicalHistory(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [[]]
2025-08-30 17:29:02 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 17:29:03 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/vitals/current
2025-08-30 17:29:03 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/labs/current
2025-08-30 17:29:03 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/vitals/current
2025-08-30 17:29:03 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/labs/current
2025-08-30 17:29:03 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/vitals/current
2025-08-30 17:29:03 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/vitals/current", parameters={}
2025-08-30 17:29:03 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentVitalSigns(Authentication)
2025-08-30 17:29:03 [http-nio-8080-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentVitalSigns(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:29:03 [http-nio-8080-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentVitalSigns(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:29:03 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [*/*]
2025-08-30 17:29:03 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 404 NOT_FOUND
2025-08-30 17:29:03 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/labs/current
2025-08-30 17:29:03 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/labs/current", parameters={}
2025-08-30 17:29:03 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentLabResults(Authentication)
2025-08-30 17:29:03 [http-nio-8080-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentLabResults(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:29:03 [http-nio-8080-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentLabResults(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 17:29:03 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [*/*]
2025-08-30 17:29:03 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 404 NOT_FOUND
2025-08-30 18:36:20 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@7bb37e8e]
2025-08-30 18:36:20 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 18:36:25 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@af94b9a]
2025-08-30 18:36:25 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 18:36:34 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@546771bd]
2025-08-30 18:36:34 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 18:36:55 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:36:55 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/selected-doctor
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/selected-doctor
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@5aeeb6f5]
2025-08-30 18:36:55 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/selected-doctor
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/selected-doctor", parameters={}
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource patients/profile/selected-doctor.]
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 404 NOT_FOUND
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-08-30 18:36:55 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-30 18:36:55 [http-nio-8080-exec-3] ERROR c.m.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-08-30 18:37:12 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@4683b758]
2025-08-30 18:37:12 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 18:37:12 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/selected-doctor
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/selected-doctor
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/selected-doctor
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/selected-doctor", parameters={}
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource patients/profile/selected-doctor.]
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 404 NOT_FOUND
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-08-30 18:37:12 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-30 18:37:12 [http-nio-8080-exec-7] ERROR c.m.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-08-30 18:37:32 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 18:37:32 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/selected-doctor
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/selected-doctor
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/selected-doctor
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/selected-doctor", parameters={}
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource patients/profile/selected-doctor.]
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 404 NOT_FOUND
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-08-30 18:37:32 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-30 18:37:32 [http-nio-8080-exec-5] ERROR c.m.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@1ff7b0f6]
2025-08-30 18:37:32 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 18:37:41 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:37:41 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/selected-doctor
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/selected-doctor
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@67c33e3]
2025-08-30 18:37:41 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/selected-doctor
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/selected-doctor", parameters={}
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource patients/profile/selected-doctor.]
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 404 NOT_FOUND
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-08-30 18:37:41 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-30 18:37:41 [http-nio-8080-exec-2] ERROR c.m.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-08-30 18:38:10 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/selected-doctor
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/selected-doctor
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/selected-doctor
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/selected-doctor", parameters={}
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.w.s.r.ResourceHttpRequestHandler - Resource not found
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.servlet.resource.NoResourceFoundException: No static resource patients/profile/selected-doctor.]
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 404 NOT_FOUND
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-08-30 18:38:10 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-08-30 18:38:10 [http-nio-8080-exec-9] ERROR c.m.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@e312e2d]
2025-08-30 18:38:10 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@48435855]
2025-08-30 18:52:57 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@790d049b]
2025-08-30 19:00:22 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@44c994aa]
2025-08-30 19:00:39 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@77b27219]
2025-08-30 19:01:58 [http-nio-8080-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@5058ffe9]
2025-08-30 19:02:19 [http-nio-8080-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@148e1835]
2025-08-30 19:03:07 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@72702606]
2025-08-30 19:03:51 [http-nio-8080-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@3ca0103a]
2025-08-30 19:04:25 [http-nio-8080-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 21:03:44 [main] INFO  c.medicalapp.MedicalAppApplication - Starting MedicalAppApplication using Java 23.0.2 with PID 22440 (G:\Projects\docfile\backend\target\classes started by DELL in G:\Projects\docfile)
2025-08-30 21:03:44 [main] DEBUG c.medicalapp.MedicalAppApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-30 21:03:44 [main] INFO  c.medicalapp.MedicalAppApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-30 21:03:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-30 21:03:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 143 ms. Found 7 MongoDB repository interfaces.
2025-08-30 21:03:48 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-30 21:03:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 21:03:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-30 21:03:48 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-08-30 21:03:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4114 ms
2025-08-30 21:03:48 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "4.11.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/23.0.2+7-58"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='madhawa', source='medical_data_db', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, streamFactoryFactory=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@7cd5fcf4], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@19cdc217, com.mongodb.Jep395RecordCodecProvider@5f5297e3, com.mongodb.KotlinCodecProvider@2d22d3b1]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[localhost:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@7ec3a8bd], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null}
2025-08-30 21:03:48 [cluster-ClusterId{value='68b319dc795ea13f884422cf', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=REPLICA_SET_PRIMARY, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=40265500, setName='rs0', canonicalAddress=127.0.0.1:27017, hosts=[127.0.0.1:27017], passives=[], arbiters=[], primary='127.0.0.1:27017', tagSet=TagSet{[]}, electionId=7fffffff0000000000000058, setVersion=1, topologyVersion=TopologyVersion{processId=68b278112d916cba60280c78, counter=6}, lastWriteDate=Sat Aug 30 21:03:43 IST 2025, lastUpdateTimeNanos=41449865236600}
2025-08-30 21:03:49 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-30 21:03:49 [main] DEBUG c.m.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-08-30 21:03:50 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 16 mappings in 'requestMappingHandlerMapping'
2025-08-30 21:03:50 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-08-30 21:03:50 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-08-30 21:03:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@42cfd794, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@43778371, org.springframework.security.web.context.SecurityContextHolderFilter@1d2c253, org.springframework.security.web.header.HeaderWriterFilter@5fe3b058, org.springframework.web.filter.CorsFilter@71beada8, org.springframework.security.web.authentication.logout.LogoutFilter@1b69fc07, com.medicalapp.security.AuthTokenFilter@6dcf7b6a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b56c222, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5a2035e1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@17f41739, org.springframework.security.web.session.SessionManagementFilter@1c411474, org.springframework.security.web.access.ExceptionTranslationFilter@5c60c08, org.springframework.security.web.access.intercept.AuthorizationFilter@1c53bd49]
2025-08-30 21:03:51 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-30 21:03:51 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-30 21:03:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-08-30 21:03:51 [main] INFO  c.medicalapp.MedicalAppApplication - Started MedicalAppApplication in 8.752 seconds (process running for 10.655)
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@4cab727c
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@35952777
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-30 21:03:52 [RMI TCP Connection(2)-**********] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-08-30 21:07:19 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /auth/signin
2025-08-30 21:07:19 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/signin
2025-08-30 21:07:19 [http-nio-8080-exec-2] WARN  o.s.w.s.h.HandlerMappingIntrospector - Cache miss for REQUEST dispatch to '/api/auth/signin' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-30 21:07:19 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/signin
2025-08-30 21:07:19 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/signin", parameters={}
2025-08-30 21:07:19 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.AuthController#authenticateUser(LoginRequest)
2025-08-30 21:07:19 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [com.medicalapp.dto.request.LoginRequest@68e7f4c4]
2025-08-30 21:07:20 [http-nio-8080-exec-2] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-08-30 21:07:20 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 21:07:20 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.dto.response.JwtResponse@1093d9ee]
2025-08-30 21:07:20 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile
2025-08-30 21:07:20 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /patients/profile/selected-doctor
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 21:07:20 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile/selected-doctor
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 21:07:20 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile/selected-doctor
2025-08-30 21:07:20 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile/selected-doctor", parameters={}
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 21:07:20 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getSelectedDoctor(Authentication)
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 21:07:20 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getSelectedDoctor(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 21:07:20 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getSelectedDoctor(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@c3b3cae]
2025-08-30 21:07:20 [http-nio-8080-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 21:07:20 [http-nio-8080-exec-6] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [*/*]
2025-08-30 21:07:20 [http-nio-8080-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 404 NOT_FOUND
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /patients/profile
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /patients/profile
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/patients/profile", parameters={}
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.medicalapp.controller.PatientController#getCurrentPatientProfile(Authentication)
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.medicalapp.controller.PatientController.getCurrentPatientProfile(org.springframework.security.core.Authentication); target is of class [com.medicalapp.controller.PatientController]
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json]
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [com.medicalapp.model.Patient@5aed8b9a]
2025-08-30 21:07:33 [http-nio-8080-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-08-30 21:07:57 [cluster-ClusterId{value='68b319dc795ea13f884422cf', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketReadException: Exception receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:730)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:606)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:451)
	at com.mongodb.internal.connection.InternalStreamConnection.receive(InternalStreamConnection.java:404)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:224)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:156)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.implRead(Socket.java:1116)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1103)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:175)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:200)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:739)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:603)
	... 5 common frames omitted
2025-08-30 21:07:57 [cluster-ClusterId{value='68b319dc795ea13f884422cf', description='null'}-localhost:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server localhost:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.lambda$open$0(SocketStream.java:84)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:84)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:211)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:196)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:156)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.net.ConnectException: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:760)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:76)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:104)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:78)
	... 4 common frames omitted
