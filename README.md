# HealthRecord - Digital Healthcare Management System

A comprehensive digital healthcare record management system built with Spring Boot backend and React Native frontend, designed to help patients manage their health information and enable healthcare providers to access patient data efficiently.

## 🏥 Features

### Patient Features
- **Digital Health Profile**: Complete patient information management
- **Vital Signs Tracking**: Blood pressure, pulse rate, respiratory rate monitoring
- **Lab Results Management**: Blood glucose, hemoglobin, cholesterol, creatinine tracking
- **Medication Management**: Current prescriptions and medication history
- **Medical History**: Complete medical record tracking
- **Allergy Management**: Comprehensive allergy tracking with severity levels
- **Health Metrics**: BMI calculation, health status indicators
- **Doctor Selection**: Choose and connect with healthcare providers

### Healthcare Provider Features
- **Patient Access**: View selected patients' complete health profiles
- **Medical Record Management**: Add and update patient records
- **Health Monitoring**: Track patient vital signs and lab results
- **Prescription Management**: Manage patient medications

### System Features
- **Secure Authentication**: JWT-based authentication with role management
- **Real-time Health Calculations**: Automatic BMI, BP categorization, lab interpretations
- **Mobile-First Design**: Responsive React Native mobile application
- **Data Security**: Encrypted data storage and secure API endpoints
- **Comprehensive Reporting**: Health metrics visualization and reporting

## 🏗️ Architecture

### Backend (Spring Boot)
- **Framework**: Spring Boot 3.x with MongoDB
- **Security**: Spring Security with JWT authentication
- **Database**: MongoDB with separate collections for optimal performance
- **API**: RESTful API design with comprehensive endpoints

### Frontend (React Native + Expo)
- **Framework**: React Native with Expo for cross-platform mobile development
- **Navigation**: React Navigation for seamless user experience
- **State Management**: Context API for authentication and app state
- **UI Components**: Custom components with Material Design principles

## 📋 Health Data Models

### Patient Profile
- Basic Information (Name, Age, Gender, Contact)
- Physical Measurements (Height, Weight, Waist Circumference)
- Health Identifiers (Health ID, Blood Group)
- Medical History and Allergies
- Insurance Information
- Emergency Contacts

### Vital Signs
- Blood Pressure (Systolic/Diastolic) with automatic categorization
- Pulse Rate (Resting) with normal range validation
- Respiratory Rate (Resting) with health indicators
- Timestamp and provider information

### Lab Results
- Blood Glucose Level with diabetes risk assessment
- Hemoglobin Level with anemia detection
- LDL Cholesterol Level with cardiovascular risk
- Serum Creatinine Level with kidney function assessment
- Laboratory and ordering physician information

### Medications
- Current and historical medications
- Dosage, frequency, and duration tracking
- Prescribing physician information
- Medication type categorization
- Active/inactive status management

### Medical Records
- Comprehensive medical history
- Visit types (Consultation, Emergency, Surgery, etc.)
- Diagnosis and treatment information
- Healthcare provider and facility details
- Follow-up scheduling
- Cost and insurance tracking

## 🚀 Getting Started

### Prerequisites
- Java 17 or higher
- Node.js 16 or higher
- MongoDB 4.4 or higher
- Expo CLI
- Android Studio or Xcode (for mobile development)

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd healthcare-record-system/backend
   ```

2. **Configure MongoDB**
   - Install and start MongoDB
   - Update `application.properties` with your MongoDB connection string

3. **Run the backend**
   ```bash
   ./mvnw spring-boot:run
   ```

   The backend will start on `http://localhost:8080`

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd ../frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Update API configuration**
   - Edit `src/services/api.js`
   - Update `BASE_URL` to your backend URL

4. **Start the development server**
   ```bash
   npm start
   ```

5. **Run on device/emulator**
   ```bash
   npm run android  # For Android
   npm run ios      # For iOS
   ```

## 📱 Mobile App Screens

### Authentication
- **Login Screen**: Secure user authentication
- **Signup Screen**: New user registration with role selection

### Main Navigation
- **Profile Tab**: Patient information and health metrics
- **Prescription Tab**: Current medications and prescriptions
- **History Tab**: Complete medical record history
- **Reports Tab**: Health metrics visualization and trends
- **Allergies Tab**: Allergy management with severity indicators

### Data Entry
- **Edit Profile**: Comprehensive patient information update
- **Vital Signs Entry**: Real-time vital signs recording with validation
- **Lab Results Entry**: Laboratory results input with health interpretations

## 🔐 Security Features

### Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (PATIENT, DOCTOR, ADMIN)
- Secure password encryption
- Session management

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration for secure cross-origin requests

## 📊 Health Calculations & Interpretations

### BMI Calculation
- Automatic BMI calculation from height and weight
- Category classification (Underweight, Normal, Overweight, Obese)
- Waist-to-height ratio calculation

### Blood Pressure Categorization
- Optimal: <120/80 mmHg
- Normal: ≤129/84 mmHg
- High Normal: 130-139/85-89 mmHg
- Grade 1 Hypertension: 140-159/90-99 mmHg
- Grade 2 Hypertension: 160-179/100-109 mmHg
- Severe Hypertension: ≥180/110 mmHg

### Lab Result Interpretations
- **Blood Glucose**: Normal, Prediabetes, Diabetes classification
- **Hemoglobin**: Anemia detection and severity assessment
- **LDL Cholesterol**: Cardiovascular risk stratification
- **Serum Creatinine**: Kidney function assessment

## 🔄 API Endpoints

### Authentication
- `POST /auth/signin` - User login
- `POST /auth/signup` - User registration
- `POST /auth/refresh` - Token refresh

### Patient Management
- `GET /patients/profile` - Get current user's profile
- `PUT /patients/profile` - Update patient profile
- `GET /patients/{id}` - Get patient by ID (doctors/admins)
- `GET /patients/by-doctor/{doctorId}` - Get patients by doctor

### Health Data
- `GET /patients/profile/vitals/current` - Current vital signs
- `GET /patients/profile/labs/current` - Current lab results
- `GET /patients/profile/medications/current` - Current medications
- `GET /patients/profile/history` - Medical history

## 📈 Future Enhancements

### Planned Features
- **QR Code Sharing**: Generate QR codes for emergency medical information
- **Telemedicine Integration**: Video consultation capabilities
- **Wearable Device Integration**: Sync with fitness trackers and health monitors
- **AI Health Insights**: Machine learning-based health recommendations
- **Multi-language Support**: Internationalization for global use
- **Offline Capability**: Local data storage for offline access

### Technical Improvements
- **Real-time Notifications**: Push notifications for appointments and medication reminders
- **Data Analytics Dashboard**: Advanced reporting and analytics for healthcare providers
- **Integration APIs**: Connect with hospital management systems
- **Blockchain Integration**: Secure, immutable health record storage

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**HealthRecord** - Empowering patients and healthcare providers with comprehensive digital health management.
