server.port=8080
server.servlet.context-path=/api

# MongoDB Configuration
spring.data.mongodb.host=localhost
#spring.data.mongodb.host=**************
spring.data.mongodb.port=27017
spring.data.mongodb.database=medical_data_db
spring.data.mongodb.authDatabase=medical_data_db
spring.data.mongodb.username=madhawa
spring.data.mongodb.password=Madhawa1988#

# JWT Configuration
jwt.secret=mySecretKey1234567890123456789012345678901234567890123456789012345678901234567890
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# QR Code Configuration
qr.expiration=3600000
qr.size=300

# CORS Configuration
cors.allowed-origins=http://localhost:8100,http://localhost:4200,capacitor://localhost,ionic://localhost
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# Logging Configuration
logging.level.com.medicalapp=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/medical-app.log

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# API Documentation
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method