package com.medicalapp.repository;

import com.medicalapp.model.VitalSigns;
import com.medicalapp.model.Patient;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface VitalSignsRepository extends MongoRepository<VitalSigns, String> {
    
    List<VitalSigns> findByPatient(Patient patient);
    
    List<VitalSigns> findByPatientId(String patientId);
    
    List<VitalSigns> findByPatientIdOrderByRecordedAtDesc(String patientId);
    
    Optional<VitalSigns> findTopByPatientIdOrderByRecordedAtDesc(String patientId);
    
    List<VitalSigns> findByPatientIdAndRecordedAtBetween(String patientId, LocalDateTime start, LocalDateTime end);
    
    @Query("{'patient.id': ?0, 'systolicBP': {$gte: ?1}}")
    List<VitalSigns> findByPatientIdAndSystolicBPGreaterThanEqual(String patientId, Double systolicBP);
    
    @Query("{'patient.id': ?0, 'diastolicBP': {$gte: ?1}}")
    List<VitalSigns> findByPatientIdAndDiastolicBPGreaterThanEqual(String patientId, Double diastolicBP);
    
    @Query("{'patient.id': ?0, 'pulseRate': {$gte: ?1, $lte: ?2}}")
    List<VitalSigns> findByPatientIdAndPulseRateBetween(String patientId, Integer minPulse, Integer maxPulse);
    
    void deleteByPatientId(String patientId);
}
