package com.medicalapp.repository;

import com.medicalapp.model.LabResults;
import com.medicalapp.model.Patient;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface LabResultsRepository extends MongoRepository<LabResults, String> {
    
    List<LabResults> findByPatient(Patient patient);
    
    List<LabResults> findByPatientId(String patientId);
    
    List<LabResults> findByPatientIdOrderByRecordedAtDesc(String patientId);
    
    Optional<LabResults> findTopByPatientIdOrderByRecordedAtDesc(String patientId);
    
    List<LabResults> findByPatientIdAndRecordedAtBetween(String patientId, LocalDateTime start, LocalDateTime end);
    
    @Query("{'patient.id': ?0, 'bloodGlucoseLevel': {$gte: ?1}}")
    List<LabResults> findByPatientIdAndBloodGlucoseLevelGreaterThanEqual(String patientId, Double glucoseLevel);
    
    @Query("{'patient.id': ?0, 'hemoglobinLevel': {$lt: ?1}}")
    List<LabResults> findByPatientIdAndHemoglobinLevelLessThan(String patientId, Double hemoglobinLevel);
    
    @Query("{'patient.id': ?0, 'ldlCholesterolLevel': {$gte: ?1}}")
    List<LabResults> findByPatientIdAndLdlCholesterolLevelGreaterThanEqual(String patientId, Double ldlLevel);
    
    @Query("{'patient.id': ?0, 'serumCreatinineLevel': {$gte: ?1}}")
    List<LabResults> findByPatientIdAndSerumCreatinineLevelGreaterThanEqual(String patientId, Double creatinineLevel);
    
    List<LabResults> findByLabName(String labName);
    
    List<LabResults> findByOrderedBy(String orderedBy);
    
    void deleteByPatientId(String patientId);
}
