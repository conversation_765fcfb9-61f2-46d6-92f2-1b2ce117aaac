package com.medicalapp.repository;

import com.medicalapp.model.QRCodeShare;
import com.medicalapp.model.Patient;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.List;

@Repository
public interface QRCodeShareRepository extends MongoRepository<QRCodeShare, String> {
    
    Optional<QRCodeShare> findByShareToken(String shareToken);
    
    List<QRCodeShare> findByPatient(Patient patient);
    
    List<QRCodeShare> findByPatientId(String patientId);
    
    List<QRCodeShare> findByIsActiveTrue();
    
    List<QRCodeShare> findByExpiresAtBefore(LocalDateTime dateTime);
    
    @Query("{'patient.id': ?0, 'isActive': true, 'expiresAt': {$gt: ?1}}")
    List<QRCodeShare> findActiveSharesByPatientId(String patientId, LocalDateTime currentTime);
    
    @Query("{'shareToken': ?0, 'isActive': true, 'expiresAt': {$gt: ?1}}")
    Optional<QRCodeShare> findActiveShareByToken(String shareToken, LocalDateTime currentTime);
    
    void deleteByExpiresAtBefore(LocalDateTime dateTime);
    
    void deleteByPatientId(String patientId);
}
