package com.medicalapp.repository;

import com.medicalapp.model.Medication;
import com.medicalapp.model.Patient;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface MedicationRepository extends MongoRepository<Medication, String> {
    
    List<Medication> findByPatient(Patient patient);
    
    List<Medication> findByPatientId(String patientId);
    
    List<Medication> findByPatientIdAndIsActiveTrue(String patientId);
    
    @Query("{'patient.id': ?0, 'isActive': true, '$or': [{'endDate': null}, {'endDate': {$gte: ?1}}]}")
    List<Medication> findCurrentMedicationsByPatientId(String patientId, LocalDate currentDate);
    
    List<Medication> findByPatientIdAndStartDateBetween(String patientId, LocalDate startDate, LocalDate endDate);
    
    @Query("{'name': {$regex: ?0, $options: 'i'}}")
    List<Medication> findByNameContainingIgnoreCase(String name);
    
    List<Medication> findByPrescribedBy(String prescribedBy);
    
    List<Medication> findByType(Medication.MedicationType type);
    
    @Query("{'patient.id': ?0, 'indication': {$regex: ?1, $options: 'i'}}")
    List<Medication> findByPatientIdAndIndicationContainingIgnoreCase(String patientId, String indication);
    
    @Query("{'patient.id': ?0, 'name': {$regex: ?1, $options: 'i'}}")
    List<Medication> findByPatientIdAndNameContainingIgnoreCase(String patientId, String name);
    
    void deleteByPatientId(String patientId);
}
