package com.medicalapp.repository;

import com.medicalapp.model.MedicalRecord;
import com.medicalapp.model.Patient;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface MedicalRecordRepository extends MongoRepository<MedicalRecord, String> {
    
    List<MedicalRecord> findByPatient(Patient patient);
    
    List<MedicalRecord> findByPatientId(String patientId);
    
    List<MedicalRecord> findByPatientIdOrderByDateDesc(String patientId);
    
    List<MedicalRecord> findByPatientIdAndType(String patientId, MedicalRecord.RecordType type);
    
    List<MedicalRecord> findByPatientIdAndDateBetween(String patientId, LocalDate startDate, LocalDate endDate);
    
    @Query("{'patient.id': ?0, 'diagnosis': {$regex: ?1, $options: 'i'}}")
    List<MedicalRecord> findByPatientIdAndDiagnosisContainingIgnoreCase(String patientId, String diagnosis);
    
    @Query("{'patient.id': ?0, 'description': {$regex: ?1, $options: 'i'}}")
    List<MedicalRecord> findByPatientIdAndDescriptionContainingIgnoreCase(String patientId, String description);
    
    List<MedicalRecord> findByProvider(String provider);
    
    List<MedicalRecord> findByFacility(String facility);
    
    List<MedicalRecord> findByType(MedicalRecord.RecordType type);
    
    @Query("{'patient.id': ?0, 'followUpDate': {$gte: ?1}}")
    List<MedicalRecord> findByPatientIdAndUpcomingFollowUp(String patientId, LocalDate currentDate);
    
    @Query("{'patient.id': ?0, 'date': {$gte: ?1}}")
    List<MedicalRecord> findRecentRecordsByPatientId(String patientId, LocalDate sinceDate);
    
    void deleteByPatientId(String patientId);
}
