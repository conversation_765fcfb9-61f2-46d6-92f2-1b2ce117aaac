package com.medicalapp.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Past;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

@Document(collection = "patients")
public class Patient {

    @Id
    private String id;

    @DBRef
    private User user;

    // Basic Identification
    private String healthId; // Health ID like 123GIGMTTG2

    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    private Gender gender;

    private String phoneNumber;

    // Physical Measurements
    private Double height; // in cm

    private Double weight; // in kg

    private Double waistCircumference; // in cm

    // Blood and Medical Info
    private String bloodGroup; // A+, B+, O+, AB+, A-, B-, O-, AB-

    private List<String> allergies = new ArrayList<>();

    private SmokingStatus smokingStatus = SmokingStatus.NO;

    // Physical Activity
    private PhysicalActivityLevel physicalActivityLevel;

    private Double metScore; // Exercise tolerance

    // Past Medical Illnesses
    private List<String> pastMedicalIllnesses = new ArrayList<>(); // DM, HTN, DL, CKD, Stroke, IHD

    // References to separate collections
    private String currentVitalSignsId; // Reference to latest VitalSigns

    private String currentLabResultsId; // Reference to latest LabResults

    // Doctor Selection
    private String selectedDoctorId; // Doctor can see patient profile if selected

    private String insuranceProvider;

    private String insurancePolicyNumber;

    private String address;

    private String emergencyContactName;

    private String emergencyContactPhone;

    private String emergencyContactRelation;

    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    public Patient() {}

    public Patient(String id, User user, String healthId, LocalDate dateOfBirth, Gender gender, String phoneNumber, Double height, Double weight, Double waistCircumference, String bloodGroup, List<String> allergies, SmokingStatus smokingStatus, PhysicalActivityLevel physicalActivityLevel, Double metScore, List<String> pastMedicalIllnesses, String currentVitalSignsId, String currentLabResultsId, String selectedDoctorId, String insuranceProvider, String insurancePolicyNumber, String address, String emergencyContactName, String emergencyContactPhone, String emergencyContactRelation, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.user = user;
        this.healthId = healthId;
        this.dateOfBirth = dateOfBirth;
        this.gender = gender;
        this.phoneNumber = phoneNumber;
        this.height = height;
        this.weight = weight;
        this.waistCircumference = waistCircumference;
        this.bloodGroup = bloodGroup;
        this.allergies = allergies;
        this.smokingStatus = smokingStatus;
        this.physicalActivityLevel = physicalActivityLevel;
        this.metScore = metScore;
        this.pastMedicalIllnesses = pastMedicalIllnesses;
        this.currentVitalSignsId = currentVitalSignsId;
        this.currentLabResultsId = currentLabResultsId;
        this.selectedDoctorId = selectedDoctorId;
        this.insuranceProvider = insuranceProvider;
        this.insurancePolicyNumber = insurancePolicyNumber;
        this.address = address;
        this.emergencyContactName = emergencyContactName;
        this.emergencyContactPhone = emergencyContactPhone;
        this.emergencyContactRelation = emergencyContactRelation;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getHealthId() {
        return healthId;
    }

    public void setHealthId(String healthId) {
        this.healthId = healthId;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getWaistCircumference() {
        return waistCircumference;
    }

    public void setWaistCircumference(Double waistCircumference) {
        this.waistCircumference = waistCircumference;
    }

    public String getBloodGroup() {
        return bloodGroup;
    }

    public void setBloodGroup(String bloodGroup) {
        this.bloodGroup = bloodGroup;
    }

    public List<String> getAllergies() {
        return allergies;
    }

    public void setAllergies(List<String> allergies) {
        this.allergies = allergies;
    }

    public SmokingStatus getSmokingStatus() {
        return smokingStatus;
    }

    public void setSmokingStatus(SmokingStatus smokingStatus) {
        this.smokingStatus = smokingStatus;
    }

    public PhysicalActivityLevel getPhysicalActivityLevel() {
        return physicalActivityLevel;
    }

    public void setPhysicalActivityLevel(PhysicalActivityLevel physicalActivityLevel) {
        this.physicalActivityLevel = physicalActivityLevel;
    }

    public Double getMetScore() {
        return metScore;
    }

    public void setMetScore(Double metScore) {
        this.metScore = metScore;
    }

    public List<String> getPastMedicalIllnesses() {
        return pastMedicalIllnesses;
    }

    public void setPastMedicalIllnesses(List<String> pastMedicalIllnesses) {
        this.pastMedicalIllnesses = pastMedicalIllnesses;
    }

    public String getCurrentVitalSignsId() {
        return currentVitalSignsId;
    }

    public void setCurrentVitalSignsId(String currentVitalSignsId) {
        this.currentVitalSignsId = currentVitalSignsId;
    }

    public String getCurrentLabResultsId() {
        return currentLabResultsId;
    }

    public void setCurrentLabResultsId(String currentLabResultsId) {
        this.currentLabResultsId = currentLabResultsId;
    }

    public String getSelectedDoctorId() {
        return selectedDoctorId;
    }

    public void setSelectedDoctorId(String selectedDoctorId) {
        this.selectedDoctorId = selectedDoctorId;
    }

    public String getInsuranceProvider() {
        return insuranceProvider;
    }

    public void setInsuranceProvider(String insuranceProvider) {
        this.insuranceProvider = insuranceProvider;
    }

    public String getInsurancePolicyNumber() {
        return insurancePolicyNumber;
    }

    public void setInsurancePolicyNumber(String insurancePolicyNumber) {
        this.insurancePolicyNumber = insurancePolicyNumber;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getEmergencyContactName() {
        return emergencyContactName;
    }

    public void setEmergencyContactName(String emergencyContactName) {
        this.emergencyContactName = emergencyContactName;
    }

    public String getEmergencyContactPhone() {
        return emergencyContactPhone;
    }

    public void setEmergencyContactPhone(String emergencyContactPhone) {
        this.emergencyContactPhone = emergencyContactPhone;
    }

    public String getEmergencyContactRelation() {
        return emergencyContactRelation;
    }

    public void setEmergencyContactRelation(String emergencyContactRelation) {
        this.emergencyContactRelation = emergencyContactRelation;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Calculated Properties
    public Double getBMI() {
        if (height != null && weight != null && height > 0) {
            double heightInMeters = height / 100.0;
            return weight / (heightInMeters * heightInMeters);
        }
        return null;
    }

    public String getBMICategory() {
        Double bmi = getBMI();
        if (bmi == null) return "Unknown";

        if (bmi < 18.5) return "Underweight";
        else if (bmi < 25) return "Normal";
        else if (bmi < 30) return "Overweight";
        else return "Obese";
    }

    public Double getWaistToHeightRatio() {
        if (waistCircumference != null && height != null && height > 0) {
            return waistCircumference / height;
        }
        return null;
    }

    public boolean isWaistCircumferenceIdeal() {
        if (waistCircumference == null || gender == null) return false;

        return switch (gender) {
            case MALE -> waistCircumference <= 90.0;
            case FEMALE -> waistCircumference <= 80.0;
            default -> false;
        };
    }

    public enum Gender {
        MALE("M"),
        FEMALE("F"),
        OTHER("O"),
        PREFER_NOT_TO_SAY("N");

        private final String code;

        Gender(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    public enum SmokingStatus {
        YES,
        NO,
        FORMER_SMOKER
    }

    public enum PhysicalActivityLevel {
        SEDENTARY("Sedentary"),
        LIGHTLY_ACTIVE("Lightly Active"),
        MODERATELY_ACTIVE("Moderately Active"),
        VERY_ACTIVE("Very Active"),
        EXTREMELY_ACTIVE("Extremely Active");

        private final String description;

        PhysicalActivityLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
