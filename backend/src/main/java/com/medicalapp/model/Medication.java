package com.medicalapp.model;


import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Document(collection = "medications")
public class Medication {
    
    @Id
    private String id;
    
    @DBRef
    private Patient patient;
    
    @NotBlank(message = "Medication name is required")
    private String name;
    
    @NotBlank(message = "Dosage is required")
    private String dosage; // e.g., "10mg", "5ml"
    
    @NotBlank(message = "Frequency is required")
    private String frequency; // e.g., "Once daily", "Twice daily", "As needed"
    
    @NotNull(message = "Start date is required")
    private LocalDate startDate;
    
    private LocalDate endDate; // null for ongoing medications
    
    private String prescribedBy; // Doctor name or ID
    
    private String indication; // What condition this medication treats
    
    private String route; // Oral, Injection, Topical, etc.
    
    private String strength; // Medication strength
    
    private String manufacturer;
    
    private String pharmacyName;
    
    private String notes;
    
    private boolean isActive = true; // Whether patient is currently taking this
    
    private MedicationType type;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    public enum MedicationType {
        PRESCRIPTION("Prescription"),
        OVER_THE_COUNTER("Over-the-Counter"),
        SUPPLEMENT("Supplement"),
        HERBAL("Herbal/Natural"),
        VACCINE("Vaccine");
        
        private final String description;
        
        MedicationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public boolean isCurrentlyTaking() {
        if (!isActive) return false;
        
        LocalDate today = LocalDate.now();
        if (startDate.isAfter(today)) return false;
        
        return endDate == null || !endDate.isBefore(today);
    }
    
    public long getDurationInDays() {
        LocalDate end = endDate != null ? endDate : LocalDate.now();
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, end);
    }
    
    public String getDisplayName() {
        return name + " " + dosage + " - " + frequency;
    }
}
