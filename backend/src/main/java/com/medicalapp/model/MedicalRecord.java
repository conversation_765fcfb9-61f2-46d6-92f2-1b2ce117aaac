package com.medicalapp.model;


import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

@Document(collection = "medical_records")
public class MedicalRecord {
    
    @Id
    private String id;
    
    @DBRef
    private Patient patient;
    
    @NotNull(message = "Record date is required")
    private LocalDate date;
    
    @NotNull(message = "Record type is required")
    private RecordType type;
    
    @NotBlank(message = "Description is required")
    private String description;
    
    private String diagnosis;
    
    private String treatment;
    
    private String provider; // Healthcare provider name
    
    private String facility; // Hospital, clinic name
    
    private String department; // Cardiology, Emergency, etc.
    
    private List<String> attachments = new ArrayList<>(); // File paths/URLs
    
    private String notes;
    
    private String followUpInstructions;
    
    private LocalDate followUpDate;
    
    private List<String> prescriptions = new ArrayList<>(); // Medication IDs
    
    private Double cost; // Medical cost if applicable
    
    private String insuranceClaim; // Insurance claim number
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    public enum RecordType {
        CONSULTATION("Consultation"),
        LAB_RESULT("Lab Result"),
        IMAGING("Imaging/Radiology"),
        PROCEDURE("Medical Procedure"),
        SURGERY("Surgery"),
        EMERGENCY_VISIT("Emergency Visit"),
        HOSPITALIZATION("Hospitalization"),
        VACCINATION("Vaccination"),
        PRESCRIPTION("Prescription"),
        FOLLOW_UP("Follow-up Visit"),
        SPECIALIST_REFERRAL("Specialist Referral"),
        PHYSICAL_EXAM("Physical Examination"),
        DENTAL("Dental Care"),
        MENTAL_HEALTH("Mental Health"),
        PREVENTIVE_CARE("Preventive Care");
        
        private final String description;
        
        RecordType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public boolean hasFollowUp() {
        return followUpDate != null && followUpDate.isAfter(LocalDate.now());
    }
    
    public boolean isRecent() {
        return date.isAfter(LocalDate.now().minusMonths(3));
    }
    
    public String getDisplayTitle() {
        return type.getDescription() + " - " + date.toString();
    }
}
