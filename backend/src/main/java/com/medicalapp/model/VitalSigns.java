package com.medicalapp.model;


import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

@Document(collection = "vital_signs")
public class VitalSigns {
    
    @Id
    private String id;
    
    @DBRef
    private Patient patient;
    
    @Min(value = 60, message = "Systolic BP should be at least 60 mmHg")
    @Max(value = 300, message = "Systolic BP should not exceed 300 mmHg")
    private Double systolicBP; // mmHg
    
    @Min(value = 40, message = "Diastolic BP should be at least 40 mmHg")
    @Max(value = 200, message = "Diastolic BP should not exceed 200 mmHg")
    private Double diastolicBP; // mmHg
    
    @Min(value = 30, message = "Pulse rate should be at least 30 bpm")
    @Max(value = 220, message = "Pulse rate should not exceed 220 bpm")
    private Integer pulseRate; // bpm (resting)
    
    @Min(value = 8, message = "Respiratory rate should be at least 8 breaths per minute")
    @Max(value = 60, message = "Respiratory rate should not exceed 60 breaths per minute")
    private Integer respiratoryRate; // breaths per minute (resting)
    
    private LocalDateTime recordedAt;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    private String recordedBy; // Healthcare provider who recorded
    
    private String notes;
    
    // Blood Pressure Category based on the provided table
    public String getBPCategory() {
        if (systolicBP == null || diastolicBP == null) return "Unknown";
        
        if (systolicBP < 120 && diastolicBP < 80) {
            return "Optimal";
        } else if (systolicBP <= 129 && diastolicBP <= 84) {
            return "Normal";
        } else if ((systolicBP >= 130 && systolicBP <= 139) || (diastolicBP >= 85 && diastolicBP <= 89)) {
            return "High Normal";
        } else if ((systolicBP >= 140 && systolicBP <= 159) || (diastolicBP >= 90 && diastolicBP <= 99)) {
            return "Grade 1 Hypertension (Mild)";
        } else if ((systolicBP >= 160 && systolicBP <= 179) || (diastolicBP >= 100 && diastolicBP <= 109)) {
            return "Grade 2 Hypertension";
        } else {
            return "Severe Hypertension";
        }
    }
    
    public boolean isNormalBP() {
        return "Optimal".equals(getBPCategory()) || "Normal".equals(getBPCategory());
    }
    
    public boolean isNormalPulse() {
        return pulseRate != null && pulseRate >= 60 && pulseRate <= 100;
    }
    
    public boolean isNormalRespiratoryRate() {
        return respiratoryRate != null && respiratoryRate >= 12 && respiratoryRate <= 20;
    }
}
