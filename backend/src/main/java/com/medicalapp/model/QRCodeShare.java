package com.medicalapp.model;


import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

@Document(collection = "qr_code_shares")
public class QRCodeShare {
    
    @Id
    private String id;
    
    @DBRef
    private Patient patient;
    
    @Indexed(unique = true)
    private String shareToken; // Unique token for QR code
    
    private LocalDateTime expiresAt;
    
    private boolean isActive = true;
    
    private List<String> sharedDataTypes = new ArrayList<>(); // What data to share
    
    private String accessLevel; // BASIC, FULL, EMERGENCY
    
    private List<AccessLog> accessLogs = new ArrayList<>();
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    // Constructor for creating new QR share
    public QRCodeShare(Patient patient, String shareToken, LocalDateTime expiresAt, 
                      List<String> sharedDataTypes, String accessLevel) {
        this.patient = patient;
        this.shareToken = shareToken;
        this.expiresAt = expiresAt;
        this.sharedDataTypes = sharedDataTypes;
        this.accessLevel = accessLevel;
    }
    
    public static class AccessLog {
        private String accessedBy; // User ID or identifier
        private LocalDateTime accessedAt;
        private String accessType; // VIEW, DOWNLOAD, etc.
        private String ipAddress;
        private String userAgent;
    }
    
    public enum AccessLevel {
        BASIC("Basic information only"),
        FULL("Complete medical profile"),
        EMERGENCY("Emergency contact and critical info");
        
        private final String description;
        
        AccessLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum SharedDataType {
        PERSONAL_INFO("Personal Information"),
        ALLERGIES("Allergies"),
        MEDICATIONS("Current Medications"),
        MEDICAL_HISTORY("Medical History"),
        EMERGENCY_CONTACT("Emergency Contact"),
        INSURANCE("Insurance Information"),
        CHRONIC_CONDITIONS("Chronic Conditions");
        
        private final String description;
        
        SharedDataType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
