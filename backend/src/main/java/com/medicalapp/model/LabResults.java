package com.medicalapp.model;


import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

@Document(collection = "lab_results")
public class LabResults {
    
    @Id
    private String id;
    
    @DBRef
    private Patient patient;
    
    @Min(value = 0, message = "Hemoglobin level cannot be negative")
    @Max(value = 25, message = "Hemoglobin level seems too high")
    private Double hemoglobinLevel; // g/dL
    
    @Min(value = 0, message = "Blood glucose level cannot be negative")
    @Max(value = 1000, message = "Blood glucose level seems too high")
    private Double bloodGlucoseLevel; // mg/dL
    
    @Min(value = 0, message = "LDL cholesterol level cannot be negative")
    @Max(value = 500, message = "LDL cholesterol level seems too high")
    private Double ldlCholesterolLevel; // mg/dL
    
    @Min(value = 0, message = "Serum creatinine level cannot be negative")
    @Max(value = 20, message = "Serum creatinine level seems too high")
    private Double serumCreatinineLevel; // mg/dL
    
    private LocalDateTime recordedAt;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    private String labName; // Laboratory where test was conducted
    
    private String orderedBy; // Healthcare provider who ordered the test
    
    private String notes;
    
    // Reference ranges and interpretations
    public String getHemoglobinStatus() {
        if (hemoglobinLevel == null) return "Unknown";
        
        // General reference ranges (may vary by lab and demographics)
        if (hemoglobinLevel < 12.0) return "Low";
        else if (hemoglobinLevel <= 16.0) return "Normal";
        else return "High";
    }
    
    public String getGlucoseStatus() {
        if (bloodGlucoseLevel == null) return "Unknown";
        
        // Fasting glucose levels
        if (bloodGlucoseLevel < 70) return "Low";
        else if (bloodGlucoseLevel <= 99) return "Normal";
        else if (bloodGlucoseLevel <= 125) return "Prediabetes";
        else return "Diabetes";
    }
    
    public String getLDLStatus() {
        if (ldlCholesterolLevel == null) return "Unknown";
        
        if (ldlCholesterolLevel < 100) return "Optimal";
        else if (ldlCholesterolLevel <= 129) return "Near Optimal";
        else if (ldlCholesterolLevel <= 159) return "Borderline High";
        else if (ldlCholesterolLevel <= 189) return "High";
        else return "Very High";
    }
    
    public String getCreatinineStatus() {
        if (serumCreatinineLevel == null) return "Unknown";
        
        // General reference ranges (may vary by gender and age)
        if (serumCreatinineLevel <= 1.2) return "Normal";
        else if (serumCreatinineLevel <= 2.0) return "Mildly Elevated";
        else if (serumCreatinineLevel <= 5.0) return "Moderately Elevated";
        else return "Severely Elevated";
    }
    
    public boolean hasAbnormalValues() {
        return !"Normal".equals(getHemoglobinStatus()) ||
               !"Normal".equals(getGlucoseStatus()) ||
               !"Optimal".equals(getLDLStatus()) ||
               !"Normal".equals(getCreatinineStatus());
    }
}
