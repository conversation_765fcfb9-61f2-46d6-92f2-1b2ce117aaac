package com.medicalapp.controller;

import com.medicalapp.model.Patient;
import com.medicalapp.model.VitalSigns;
import com.medicalapp.model.LabResults;
import com.medicalapp.model.Medication;
import com.medicalapp.model.MedicalRecord;
import com.medicalapp.model.User;
import com.medicalapp.repository.PatientRepository;
import com.medicalapp.repository.VitalSignsRepository;
import com.medicalapp.repository.LabResultsRepository;
import com.medicalapp.repository.MedicationRepository;
import com.medicalapp.repository.MedicalRecordRepository;
import com.medicalapp.repository.UserRepository;
import com.medicalapp.security.UserPrincipal;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/patients")
public class PatientController {

    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private VitalSignsRepository vitalSignsRepository;

    @Autowired
    private LabResultsRepository labResultsRepository;

    @Autowired
    private MedicationRepository medicationRepository;

    @Autowired
    private MedicalRecordRepository medicalRecordRepository;

    @Autowired
    private UserRepository userRepository;

    // Get current user's patient profile
    @GetMapping("/profile")
    @PreAuthorize("hasRole('PATIENT') or hasRole('ADMIN')")
    public ResponseEntity<?> getCurrentPatientProfile(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Optional<User> user = userRepository.findById(userPrincipal.getId());
        
        if (user.isPresent()) {
            Optional<Patient> patient = patientRepository.findByUser(user.get());
            if (patient.isPresent()) {
                return ResponseEntity.ok(patient.get());
            }
        }
        
        return ResponseEntity.notFound().build();
    }

    // Update current user's patient profile
    @PutMapping("/profile")
    @PreAuthorize("hasRole('PATIENT') or hasRole('ADMIN')")
    public ResponseEntity<?> updateCurrentPatientProfile(@Valid @RequestBody Patient patientDetails, 
                                                        Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Optional<User> user = userRepository.findById(userPrincipal.getId());
        
        if (user.isPresent()) {
            Optional<Patient> existingPatient = patientRepository.findByUser(user.get());
            if (existingPatient.isPresent()) {
                Patient patient = existingPatient.get();
                
                // Update basic information
                patient.setHealthId(patientDetails.getHealthId());
                patient.setDateOfBirth(patientDetails.getDateOfBirth());
                patient.setGender(patientDetails.getGender());
                patient.setPhoneNumber(patientDetails.getPhoneNumber());
                patient.setHeight(patientDetails.getHeight());
                patient.setWeight(patientDetails.getWeight());
                patient.setWaistCircumference(patientDetails.getWaistCircumference());
                patient.setBloodGroup(patientDetails.getBloodGroup());
                patient.setAllergies(patientDetails.getAllergies());
                patient.setSmokingStatus(patientDetails.getSmokingStatus());
                patient.setPhysicalActivityLevel(patientDetails.getPhysicalActivityLevel());
                patient.setMetScore(patientDetails.getMetScore());
                patient.setPastMedicalIllnesses(patientDetails.getPastMedicalIllnesses());
                patient.setSelectedDoctorId(patientDetails.getSelectedDoctorId());
                patient.setInsuranceProvider(patientDetails.getInsuranceProvider());
                patient.setInsurancePolicyNumber(patientDetails.getInsurancePolicyNumber());
                patient.setAddress(patientDetails.getAddress());
                patient.setEmergencyContactName(patientDetails.getEmergencyContactName());
                patient.setEmergencyContactPhone(patientDetails.getEmergencyContactPhone());
                patient.setEmergencyContactRelation(patientDetails.getEmergencyContactRelation());
                
                Patient savedPatient = patientRepository.save(patient);
                return ResponseEntity.ok(savedPatient);
            }
        }
        
        return ResponseEntity.notFound().build();
    }

    // Get patient by ID (for doctors/admins)
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('DOCTOR') or hasRole('ADMIN')")
    public ResponseEntity<Patient> getPatientById(@PathVariable String id) {
        Optional<Patient> patient = patientRepository.findById(id);
        return patient.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    // Get all patients (for admins)
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<Patient>> getAllPatients() {
        List<Patient> patients = patientRepository.findAll();
        return ResponseEntity.ok(patients);
    }

    // Get patients by doctor (for doctors to see their patients)
    @GetMapping("/by-doctor/{doctorId}")
    @PreAuthorize("hasRole('DOCTOR') or hasRole('ADMIN')")
    public ResponseEntity<List<Patient>> getPatientsByDoctor(@PathVariable String doctorId) {
        List<Patient> patients = patientRepository.findAll().stream()
                .filter(patient -> doctorId.equals(patient.getSelectedDoctorId()))
                .toList();
        return ResponseEntity.ok(patients);
    }

    // Get current vital signs
    @GetMapping("/profile/vitals/current")
    @PreAuthorize("hasRole('PATIENT') or hasRole('ADMIN')")
    public ResponseEntity<?> getCurrentVitalSigns(Authentication authentication) {
        String patientId = getPatientIdFromAuth(authentication);
        if (patientId != null) {
            Optional<VitalSigns> vitalSigns = vitalSignsRepository.findTopByPatientIdOrderByRecordedAtDesc(patientId);
            return vitalSigns.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
        }
        return ResponseEntity.notFound().build();
    }

    // Get current lab results
    @GetMapping("/profile/labs/current")
    @PreAuthorize("hasRole('PATIENT') or hasRole('ADMIN')")
    public ResponseEntity<?> getCurrentLabResults(Authentication authentication) {
        String patientId = getPatientIdFromAuth(authentication);
        if (patientId != null) {
            Optional<LabResults> labResults = labResultsRepository.findTopByPatientIdOrderByRecordedAtDesc(patientId);
            return labResults.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
        }
        return ResponseEntity.notFound().build();
    }

    // Get current medications
    @GetMapping("/profile/medications/current")
    @PreAuthorize("hasRole('PATIENT') or hasRole('ADMIN')")
    public ResponseEntity<?> getCurrentMedications(Authentication authentication) {
        String patientId = getPatientIdFromAuth(authentication);
        if (patientId != null) {
            List<Medication> medications = medicationRepository.findCurrentMedicationsByPatientId(patientId, LocalDate.now());
            return ResponseEntity.ok(medications);
        }
        return ResponseEntity.notFound().build();
    }

    // Get medical history
    @GetMapping("/profile/history")
    @PreAuthorize("hasRole('PATIENT') or hasRole('ADMIN')")
    public ResponseEntity<?> getMedicalHistory(Authentication authentication) {
        String patientId = getPatientIdFromAuth(authentication);
        if (patientId != null) {
            List<MedicalRecord> history = medicalRecordRepository.findByPatientIdOrderByDateDesc(patientId);
            return ResponseEntity.ok(history);
        }
        return ResponseEntity.notFound().build();
    }

    private String getPatientIdFromAuth(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Optional<User> user = userRepository.findById(userPrincipal.getId());
        
        if (user.isPresent()) {
            Optional<Patient> patient = patientRepository.findByUser(user.get());
            return patient.map(Patient::getId).orElse(null);
        }
        return null;
    }
}
