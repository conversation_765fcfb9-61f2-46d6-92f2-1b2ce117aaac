<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">
    
    <servers>
        <server>
            <id>viganana2</id>
            <username>YOUR_TOMCAT_USERNAME</username>
            <password>YOUR_TOMCAT_PASSWORD</password>
        </server>
    </servers>
    
</settings>

<!-- 
Instructions:
1. Copy this file to ~/.m2/settings.xml (or %USERPROFILE%\.m2\settings.xml on Windows)
2. Replace YOUR_TOMCAT_USERNAME and YOUR_TOMCAT_PASSWORD with actual credentials
3. Make sure the server id "viganana2" matches the one in pom.xml
-->
