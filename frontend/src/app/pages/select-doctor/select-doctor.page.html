<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{translate('doctor.select_title')}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="select-doctor-container">
    
    <!-- Current Selected Doctor -->
    <ion-card *ngIf="selectedDoctor" class="selected-doctor-card">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="checkmark-circle" color="success"></ion-icon>
          {{translate('profile.selected_doctor')}}
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="doctor-info">
          <div class="doctor-avatar">
            <ion-icon name="person-circle" size="large"></ion-icon>
          </div>
          <div class="doctor-details">
            <h3>{{getDoctorDisplayName(selectedDoctor)}}</h3>
            <p>{{getDoctorSpecialization(selectedDoctor)}}</p>
            <p *ngIf="selectedDoctor.email">{{selectedDoctor.email}}</p>
            <p *ngIf="selectedDoctor.phoneNumber">{{selectedDoctor.phoneNumber}}</p>
          </div>
        </div>
        <ion-button fill="outline" color="danger" (click)="removeDoctor()" class="remove-button">
          <ion-icon name="trash-outline" slot="start"></ion-icon>
          Remove Doctor
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Search Bar -->
    <ion-searchbar 
      [(ngModel)]="searchTerm" 
      (ionInput)="filterDoctors()"
      [placeholder]="translate('doctor.search_placeholder')"
      show-clear-button="focus">
    </ion-searchbar>

    <!-- Loading -->
    <div *ngIf="loading" class="loading-container">
      <ion-spinner></ion-spinner>
      <p>{{translate('message.loading')}}</p>
    </div>

    <!-- Doctors List -->
    <div *ngIf="!loading">
      <ion-card *ngFor="let doctor of filteredDoctors" 
               class="doctor-card" 
               [class.selected]="isSelected(doctor)"
               (click)="selectDoctor(doctor)">
        <ion-card-content>
          <div class="doctor-info">
            <div class="doctor-avatar">
              <ion-icon name="person-circle" size="large" 
                       [color]="isSelected(doctor) ? 'success' : 'medium'"></ion-icon>
            </div>
            <div class="doctor-details">
              <h3>{{getDoctorDisplayName(doctor)}}</h3>
              <p class="specialization">
                <ion-icon name="medical-outline"></ion-icon>
                {{getDoctorSpecialization(doctor)}}
              </p>
              <p class="contact-info" *ngIf="doctor.email">
                <ion-icon name="mail-outline"></ion-icon>
                {{doctor.email}}
              </p>
              <p class="contact-info" *ngIf="doctor.phoneNumber">
                <ion-icon name="call-outline"></ion-icon>
                {{doctor.phoneNumber}}
              </p>
              <div class="doctor-stats" *ngIf="doctor.experience || doctor.rating">
                <ion-chip *ngIf="doctor.experience" color="primary" outline>
                  <ion-icon name="time-outline"></ion-icon>
                  <ion-label>{{doctor.experience}} years</ion-label>
                </ion-chip>
                <ion-chip *ngIf="doctor.rating" color="warning" outline>
                  <ion-icon name="star-outline"></ion-icon>
                  <ion-label>{{doctor.rating}}/5</ion-label>
                </ion-chip>
              </div>
            </div>
            <div class="selection-indicator" *ngIf="isSelected(doctor)">
              <ion-icon name="checkmark-circle" color="success" size="large"></ion-icon>
            </div>
          </div>
        </ion-card-content>
      </ion-card>

      <!-- No Doctors Found -->
      <div *ngIf="filteredDoctors.length === 0" class="no-doctors">
        <ion-icon name="medical-outline" size="large"></ion-icon>
        <h3>{{translate('doctor.no_doctors')}}</h3>
        <p>Try adjusting your search criteria</p>
      </div>
    </div>

    <!-- Help Text -->
    <ion-card class="help-card" *ngIf="!selectedDoctor">
      <ion-card-content>
        <div class="help-content">
          <ion-icon name="information-circle-outline" color="primary"></ion-icon>
          <div>
            <h4>Why select a doctor?</h4>
            <p>Selecting a family doctor or consultant allows them to view your health profile and provide better care coordination.</p>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
