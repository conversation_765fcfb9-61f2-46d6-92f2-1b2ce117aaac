import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Alert<PERSON>ontroller, LoadingController, ToastController } from '@ionic/angular';
import { ApiService } from '../../services/api.service';
import { LanguageService } from '../../services/language.service';

@Component({
  selector: 'app-select-doctor',
  templateUrl: './select-doctor.page.html',
  styleUrls: ['./select-doctor.page.scss'],
})
export class SelectDoctorPage implements OnInit {
  doctors: any[] = [];
  filteredDoctors: any[] = [];
  selectedDoctor: any = null;
  searchTerm: string = '';
  loading = true;

  constructor(
    private apiService: ApiService,
    private languageService: LanguageService,
    private router: Router,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private toastController: ToastController
  ) {}

  ngOnInit() {
    this.loadDoctors();
    this.loadSelectedDoctor();
  }

  async loadDoctors() {
    try {
      const response = await (await this.apiService.getAllDoctors()).toPromise();
      this.doctors = response || [];
      this.filteredDoctors = [...this.doctors];
    } catch (error) {
      console.error('Error loading doctors:', error);
      const toast = await this.toastController.create({
        message: this.languageService.translate('message.error'),
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      this.loading = false;
    }
  }

  async loadSelectedDoctor() {
    try {
      const response = await (await this.apiService.getSelectedDoctor()).toPromise();
      this.selectedDoctor = response;
    } catch (error) {
      // No selected doctor is fine
      console.log('No selected doctor found');
    }
  }

  filterDoctors() {
    if (!this.searchTerm.trim()) {
      this.filteredDoctors = [...this.doctors];
    } else {
      const term = this.searchTerm.toLowerCase();
      this.filteredDoctors = this.doctors.filter(doctor => 
        doctor.firstName.toLowerCase().includes(term) ||
        doctor.lastName.toLowerCase().includes(term) ||
        doctor.email.toLowerCase().includes(term) ||
        (doctor.specialization && doctor.specialization.toLowerCase().includes(term))
      );
    }
  }

  async selectDoctor(doctor: any) {
    const alert = await this.alertController.create({
      header: this.languageService.translate('doctor.select_title'),
      message: `${this.languageService.translate('action.select')} Dr. ${doctor.firstName} ${doctor.lastName}?`,
      buttons: [
        {
          text: this.languageService.translate('action.cancel'),
          role: 'cancel'
        },
        {
          text: this.languageService.translate('action.select'),
          handler: async () => {
            await this.confirmDoctorSelection(doctor);
          }
        }
      ]
    });
    await alert.present();
  }

  async confirmDoctorSelection(doctor: any) {
    const loading = await this.loadingController.create({
      message: this.languageService.translate('message.saving')
    });
    await loading.present();

    try {
      // Update profile with selected doctor ID
      const profileData = { selectedDoctorId: doctor.id };
      await (await this.apiService.updateProfile(profileData)).toPromise();
      
      this.selectedDoctor = doctor;
      
      const toast = await this.toastController.create({
        message: this.languageService.translate('message.doctor_selected'),
        duration: 3000,
        color: 'success'
      });
      await toast.present();
      
      // Navigate back to profile
      this.router.navigate(['/tabs/profile']);
    } catch (error) {
      console.error('Error selecting doctor:', error);
      const toast = await this.toastController.create({
        message: this.languageService.translate('message.error'),
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      await loading.dismiss();
    }
  }

  async removeDoctor() {
    const alert = await this.alertController.create({
      header: 'Remove Doctor',
      message: 'Are you sure you want to remove your selected doctor?',
      buttons: [
        {
          text: this.languageService.translate('action.cancel'),
          role: 'cancel'
        },
        {
          text: 'Remove',
          role: 'destructive',
          handler: async () => {
            await this.confirmDoctorRemoval();
          }
        }
      ]
    });
    await alert.present();
  }

  async confirmDoctorRemoval() {
    const loading = await this.loadingController.create({
      message: this.languageService.translate('message.saving')
    });
    await loading.present();

    try {
      const profileData = { selectedDoctorId: null };
      await (await this.apiService.updateProfile(profileData)).toPromise();
      
      this.selectedDoctor = null;
      
      const toast = await this.toastController.create({
        message: 'Doctor removed successfully',
        duration: 3000,
        color: 'success'
      });
      await toast.present();
    } catch (error) {
      console.error('Error removing doctor:', error);
      const toast = await this.toastController.create({
        message: this.languageService.translate('message.error'),
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      await loading.dismiss();
    }
  }

  getDoctorDisplayName(doctor: any): string {
    return `Dr. ${doctor.firstName} ${doctor.lastName}`;
  }

  getDoctorSpecialization(doctor: any): string {
    return doctor.specialization || 'General Practice';
  }

  isSelected(doctor: any): boolean {
    return this.selectedDoctor && this.selectedDoctor.id === doctor.id;
  }

  goBack() {
    this.router.navigate(['/tabs/profile']);
  }

  // Language service methods
  translate(key: string): string {
    return this.languageService.translate(key);
  }
}
