.select-doctor-container {
  padding: 16px;
  padding-bottom: 32px;
}

.selected-doctor-card {
  border: 2px solid #27ae60;
  border-radius: 12px;
  margin-bottom: 20px;
  
  ion-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #27ae60;
    
    ion-icon {
      font-size: 1.2rem;
    }
  }
}

.doctor-card {
  margin-bottom: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &.selected {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.05);
  }
}

.doctor-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  position: relative;
}

.doctor-avatar {
  flex-shrink: 0;
  
  ion-icon {
    font-size: 3rem;
    color: #7f8c8d;
  }
}

.doctor-details {
  flex: 1;
  
  h3 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
  }
  
  .specialization {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 4px 0;
    color: #e74c3c;
    font-weight: 500;
    
    ion-icon {
      font-size: 0.9rem;
    }
  }
  
  .contact-info {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 4px 0;
    color: #7f8c8d;
    font-size: 0.9rem;
    
    ion-icon {
      font-size: 0.8rem;
    }
  }
}

.doctor-stats {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
  
  ion-chip {
    font-size: 0.8rem;
    height: 28px;
    
    ion-icon {
      font-size: 0.7rem;
      margin-right: 4px;
    }
  }
}

.selection-indicator {
  position: absolute;
  top: 0;
  right: 0;
  
  ion-icon {
    font-size: 2rem;
  }
}

.remove-button {
  margin-top: 16px;
  --border-color: #e74c3c;
  --color: #e74c3c;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: #7f8c8d;
    margin: 0;
  }
}

.no-doctors {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
  
  ion-icon {
    font-size: 4rem;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  h3 {
    margin: 0 0 8px 0;
    color: #2c3e50;
  }
  
  p {
    margin: 0;
    font-size: 0.9rem;
  }
}

.help-card {
  margin-top: 20px;
  border-left: 4px solid #3498db;
  
  .help-content {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    
    ion-icon {
      font-size: 1.5rem;
      margin-top: 4px;
      flex-shrink: 0;
    }
    
    h4 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 1rem;
    }
    
    p {
      margin: 0;
      color: #7f8c8d;
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }
}

// Search bar styling
ion-searchbar {
  --background: #f8f9fa;
  --border-radius: 12px;
  margin-bottom: 16px;
}

// Responsive design
@media (max-width: 768px) {
  .select-doctor-container {
    padding: 12px;
  }
  
  .doctor-info {
    gap: 12px;
  }
  
  .doctor-avatar ion-icon {
    font-size: 2.5rem;
  }
  
  .doctor-details h3 {
    font-size: 1rem;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .selected-doctor-card {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
  }
  
  .doctor-card {
    --background: #2c3e50;
    
    &.selected {
      background: rgba(39, 174, 96, 0.1);
    }
  }
  
  .doctor-details h3 {
    color: #ecf0f1;
  }
  
  .doctor-details .contact-info {
    color: #bdc3c7;
  }
  
  .no-doctors h3 {
    color: #ecf0f1;
  }
  
  .help-card {
    --background: #2c3e50;
    border-left-color: #3498db;
    
    .help-content h4 {
      color: #ecf0f1;
    }
    
    .help-content p {
      color: #bdc3c7;
    }
  }
}
