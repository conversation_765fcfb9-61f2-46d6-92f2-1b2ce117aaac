import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Alert<PERSON>ontroller, LoadingController, ActionSheetController } from '@ionic/angular';
import { AuthService, User } from '../../services/auth.service';
import { ApiService } from '../../services/api.service';
import { LanguageService } from '../../services/language.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
})
export class ProfilePage implements OnInit {
  profile: any = null;
  user: User | null = null;
  selectedDoctor: any = null;
  loading = true;
  currentLanguage = 'en';

  constructor(
    private authService: AuthService,
    private apiService: ApiService,
    private languageService: LanguageService,
    private router: Router,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private actionSheetController: ActionSheetController
  ) {}

  ngOnInit() {
    this.authService.user$.subscribe(user => {
      this.user = user;
    });

    this.languageService.currentLanguage$.subscribe(lang => {
      this.currentLanguage = lang;
    });

    this.loadProfile();
    this.loadSelectedDoctor();
  }

  async loadProfile() {
    try {
      const response = await (await this.apiService.getProfile()).toPromise();
      this.profile = response;
    } catch (error) {
      console.error('Error loading profile:', error);
      const alert = await this.alertController.create({
        header: 'Error',
        message: 'Failed to load profile',
        buttons: ['OK']
      });
      await alert.present();
    } finally {
      this.loading = false;
    }
  }

  async doRefresh(event: any) {
    await this.loadProfile();
    event.target.complete();
  }

  async handleLogout() {
    const alert = await this.alertController.create({
      header: 'Logout',
      message: 'Are you sure you want to logout?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Logout',
          role: 'destructive',
          handler: async () => {
            await this.authService.logout();
            this.router.navigate(['/login']);
          }
        }
      ]
    });
    await alert.present();
  }

  calculateAge(dateOfBirth: string): string {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age.toString();
  }

  getBMI(): string {
    if (this.profile?.height && this.profile?.weight) {
      const heightInMeters = this.profile.height / 100;
      const bmi = this.profile.weight / (heightInMeters * heightInMeters);
      return bmi.toFixed(1);
    }
    return 'N/A';
  }

  getBMICategory(): string {
    const bmi = parseFloat(this.getBMI());
    if (isNaN(bmi)) return 'Unknown';

    if (bmi < 18.5) return this.translate('bmi.underweight');
    else if (bmi < 25) return this.translate('bmi.normal');
    else if (bmi < 30) return this.translate('bmi.overweight');
    else return this.translate('bmi.obese');
  }

  getBMIColor(): string {
    const bmi = parseFloat(this.getBMI());
    if (isNaN(bmi)) return 'medium';

    if (bmi < 18.5) return 'warning';
    else if (bmi < 25) return 'success';
    else if (bmi < 30) return 'warning';
    else return 'danger';
  }

  getBMIPosition(): number {
    const bmi = parseFloat(this.getBMI());
    if (isNaN(bmi)) return 0;

    // Map BMI to percentage position on scale (15-40 BMI range)
    const minBMI = 15;
    const maxBMI = 40;
    const position = ((bmi - minBMI) / (maxBMI - minBMI)) * 100;
    return Math.max(0, Math.min(100, position));
  }

  // Waist to Height Ratio Calculation
  calculateWaistToHeightRatio(): string {
    if (this.profile?.waistCircumference && this.profile?.height) {
      const ratio = this.profile.waistCircumference / this.profile.height;
      return ratio.toFixed(2);
    }
    return 'N/A';
  }

  getWaistToHeightRatioCategory(): string {
    const ratio = parseFloat(this.calculateWaistToHeightRatio());
    if (isNaN(ratio)) return '';

    if (ratio < 0.5) return this.translate('status.ideal');
    return this.translate('status.above_ideal');
  }

  getWaistToHeightRatioColor(): string {
    const ratio = parseFloat(this.calculateWaistToHeightRatio());
    if (isNaN(ratio)) return 'medium';

    return ratio < 0.5 ? 'success' : 'warning';
  }

  // Blood Pressure Interpretation
  getBloodPressureCategory(): string {
    // This would need to be loaded from current vital signs
    // For now, return placeholder
    return 'No recent data';
  }

  getBloodPressureColor(): string {
    return 'medium';
  }

  async loadSelectedDoctor() {
    try {
      const response = await (await this.apiService.getSelectedDoctor()).toPromise();
      this.selectedDoctor = response;
    } catch (error) {
      // No selected doctor is fine
      this.selectedDoctor = null;
    }
  }

  editProfile() {
    // Navigate to edit profile page
    this.router.navigate(['/edit-profile']);
  }

  selectDoctor() {
    this.router.navigate(['/select-doctor']);
  }

  async showLanguageOptions() {
    const actionSheet = await this.actionSheetController.create({
      header: this.translate('language.switch'),
      buttons: [
        {
          text: 'English',
          icon: 'language-outline',
          handler: () => {
            this.languageService.setLanguage('en');
          }
        },
        {
          text: 'සිංහල',
          icon: 'language-outline',
          handler: () => {
            this.languageService.setLanguage('si');
          }
        },
        {
          text: this.translate('action.cancel'),
          icon: 'close',
          role: 'cancel'
        }
      ]
    });
    await actionSheet.present();
  }

  getDoctorDisplayName(): string {
    if (!this.selectedDoctor) return this.translate('profile.no_doctor_selected');
    return `Dr. ${this.selectedDoctor.firstName} ${this.selectedDoctor.lastName}`;
  }

  // Language service methods
  translate(key: string): string {
    return this.languageService.translate(key);
  }
}
