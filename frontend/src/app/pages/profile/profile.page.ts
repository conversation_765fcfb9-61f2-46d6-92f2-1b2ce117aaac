import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController, LoadingController } from '@ionic/angular';
import { AuthService, User } from '../../services/auth.service';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
})
export class ProfilePage implements OnInit {
  profile: any = null;
  user: User | null = null;
  loading = true;

  constructor(
    private authService: AuthService,
    private apiService: ApiService,
    private router: Router,
    private alertController: AlertController,
    private loadingController: LoadingController
  ) {}

  ngOnInit() {
    this.authService.user$.subscribe(user => {
      this.user = user;
    });
    this.loadProfile();
  }

  async loadProfile() {
    try {
      const response = await (await this.apiService.getProfile()).toPromise();
      this.profile = response;
    } catch (error) {
      console.error('Error loading profile:', error);
      const alert = await this.alertController.create({
        header: 'Error',
        message: 'Failed to load profile',
        buttons: ['OK']
      });
      await alert.present();
    } finally {
      this.loading = false;
    }
  }

  async doRefresh(event: any) {
    await this.loadProfile();
    event.target.complete();
  }

  async handleLogout() {
    const alert = await this.alertController.create({
      header: 'Logout',
      message: 'Are you sure you want to logout?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Logout',
          role: 'destructive',
          handler: async () => {
            await this.authService.logout();
            this.router.navigate(['/login']);
          }
        }
      ]
    });
    await alert.present();
  }

  calculateAge(dateOfBirth: string): string {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age.toString();
  }

  getBMI(): string {
    if (this.profile?.height && this.profile?.weight) {
      const heightInMeters = this.profile.height / 100;
      const bmi = this.profile.weight / (heightInMeters * heightInMeters);
      return bmi.toFixed(1);
    }
    return 'N/A';
  }

  getBMICategory(): string {
    const bmi = parseFloat(this.getBMI());
    if (isNaN(bmi)) return 'Unknown';
    
    if (bmi < 18.5) return 'Underweight';
    else if (bmi < 25) return 'Normal';
    else if (bmi < 30) return 'Overweight';
    else return 'Obese';
  }

  getBMIColor(): string {
    const category = this.getBMICategory();
    switch (category) {
      case 'Underweight': return 'warning';
      case 'Normal': return 'success';
      case 'Overweight': return 'warning';
      case 'Obese': return 'danger';
      default: return 'medium';
    }
  }

  editProfile() {
    // Navigate to edit profile page
    this.router.navigate(['/edit-profile']);
  }
}
