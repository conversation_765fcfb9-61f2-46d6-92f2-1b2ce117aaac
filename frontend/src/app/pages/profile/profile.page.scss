// Profile page - Bright & Clean styles

.profile-container {
  padding: 12px;
  padding-bottom: 24px;
  max-width: 100%;
  margin: 0;
  background: #ffffff;
  min-height: 100vh;
}

.header-card {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  color: white;
  margin: 0 0 16px 0;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2) !important;

  ion-card-content {
    padding: 20px;
  }
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255, 255, 255, 0.2);

  ion-icon {
    font-size: 3rem;
    color: white;
  }
}

.user-info {
  flex: 1;

  h2 {
    margin: 0 0 8px 0;
    color: white;
    font-weight: var(--heading-font-weight);
    font-size: 1.4rem;
  }

  p {
    margin: 0 0 12px 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
  }

  ion-chip {
    --background: rgba(255, 255, 255, 0.2);
    --color: white;
    font-weight: 600;
    border-radius: 20px;
  }
}

.edit-button {
  margin: 16px 12px 12px 12px;
  --background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  --background-activated: linear-gradient(135deg, var(--ion-color-primary-shade) 0%, var(--ion-color-secondary-shade) 100%);
  --color: white;
  height: 48px;
  font-weight: 600;
  border-radius: 10px;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);

  ion-icon {
    font-size: 1.1rem;
  }
}
