.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
}

.profile-container {
  padding: 16px;
}

.header-card {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  
  ion-card-content {
    padding: 20px;
  }
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  
  ion-icon {
    color: white;
    font-size: 40px;
  }
}

.user-info {
  flex: 1;
  
  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: bold;
  }
  
  p {
    margin: 0 0 8px 0;
    opacity: 0.9;
  }
}

ion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

ion-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2c3e50;
  
  ion-icon {
    color: #e74c3c;
  }
}

ion-item {
  --border-color: transparent;
  --inner-border-width: 0;
  
  ion-label h3 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  ion-label p {
    color: #7f8c8d;
    margin: 0;
  }
}

.edit-button {
  margin: 20px 0;
  --border-color: #e74c3c;
  --color: #e74c3c;
}
