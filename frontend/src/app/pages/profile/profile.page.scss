// Profile page uses global styles, only specific overrides here

.profile-container {
  padding: 16px;
  padding-bottom: 32px;
  max-width: 600px;
  margin: 0 auto;
}

.header-card {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  color: white;
  margin-bottom: 20px;
  border-radius: var(--card-border-radius) !important;
  box-shadow: var(--card-shadow) !important;

  ion-card-content {
    padding: var(--card-padding);
  }
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255, 255, 255, 0.2);

  ion-icon {
    font-size: 3rem;
    color: white;
  }
}

.user-info {
  flex: 1;

  h2 {
    margin: 0 0 8px 0;
    color: white;
    font-weight: var(--heading-font-weight);
    font-size: 1.4rem;
  }

  p {
    margin: 0 0 12px 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
  }

  ion-chip {
    --background: rgba(255, 255, 255, 0.2);
    --color: white;
    font-weight: 600;
    border-radius: 20px;
  }
}

.edit-button {
  margin: 24px 16px 16px 16px;
  --background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  --background-activated: linear-gradient(135deg, var(--ion-color-primary-shade) 0%, #68428f 100%);
  --color: white;
  height: 56px;
  font-weight: 600;
  border-radius: 16px;
  font-size: 1.1rem;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);

  ion-icon {
    font-size: 1.2rem;
  }
}
