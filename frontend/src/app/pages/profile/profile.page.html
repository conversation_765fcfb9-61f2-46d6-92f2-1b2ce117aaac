<ion-header>
  <ion-toolbar>
    <ion-title>{{translate('profile.title')}}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="showLanguageOptions()">
        <ion-icon name="language-outline"></ion-icon>
      </ion-button>
      <ion-button (click)="handleLogout()">
        <ion-icon name="log-out-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div *ngIf="loading" class="loading-container">
    <ion-spinner></ion-spinner>
    <p>Loading...</p>
  </div>

  <div *ngIf="!loading && profile" class="profile-container">
    <!-- Header Card -->
    <ion-card class="header-card">
      <ion-card-content>
        <div class="profile-header">
          <div class="avatar">
            <ion-icon name="person" size="large"></ion-icon>
          </div>
          <div class="user-info">
            <h2>{{profile.firstName}} {{profile.lastName}}</h2>
            <p>{{user?.email}}</p>
            <ion-chip color="primary">
              <ion-label>{{user?.role}}</ion-label>
            </ion-chip>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Basic Information -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="person-outline"></ion-icon>
          Basic Information
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-label>
              <h3>Age</h3>
              <p>{{calculateAge(profile.dateOfBirth)}} years</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Gender</h3>
              <p>{{profile.gender || 'Not specified'}}</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Blood Group</h3>
              <p>{{profile.bloodGroup || 'Not specified'}}</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Health ID</h3>
              <p>{{profile.healthId || 'Not assigned'}}</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Physical Measurements -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="fitness-outline"></ion-icon>
          Physical Measurements
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-label>
              <h3>Height</h3>
              <p>{{profile.height || 'Not recorded'}} cm</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Weight</h3>
              <p>{{profile.weight || 'Not recorded'}} kg</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>BMI</h3>
              <p>
                {{getBMI()}}
                <ion-chip [color]="getBMIColor()" *ngIf="getBMI() !== 'N/A'">
                  <ion-label>{{getBMICategory()}}</ion-label>
                </ion-chip>
              </p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Selected Doctor -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="medical-outline"></ion-icon>
          {{translate('profile.selected_doctor')}}
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item button (click)="selectDoctor()">
          <ion-icon name="person-circle-outline" slot="start" [color]="selectedDoctor ? 'success' : 'medium'"></ion-icon>
          <ion-label>
            <h3>{{getDoctorDisplayName()}}</h3>
            <p *ngIf="selectedDoctor">{{selectedDoctor.email}}</p>
            <p *ngIf="!selectedDoctor">{{translate('profile.select_doctor')}}</p>
          </ion-label>
          <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- Contact Information -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="call-outline"></ion-icon>
          {{translate('profile.contact_info')}}
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-label>
              <h3>Phone</h3>
              <p>{{profile.phoneNumber || 'Not provided'}}</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Address</h3>
              <p>{{profile.address || 'Not provided'}}</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Emergency Contact</h3>
              <p>{{profile.emergencyContactName || 'Not provided'}}</p>
              <p *ngIf="profile.emergencyContactPhone">{{profile.emergencyContactPhone}}</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Edit Button -->
    <ion-button expand="block" fill="outline" (click)="editProfile()" class="edit-button">
      <ion-icon name="create-outline" slot="start"></ion-icon>
      {{translate('profile.edit')}}
    </ion-button>
  </div>
</ion-content>
