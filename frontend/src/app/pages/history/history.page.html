<ion-header>
  <ion-toolbar>
    <ion-title>Medical History</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div *ngIf="loading" class="loading-container">
    <ion-spinner></ion-spinner>
    <p>Loading medical history...</p>
  </div>

  <div *ngIf="!loading" class="content-container">
    <ion-card *ngFor="let record of medicalHistory">
      <ion-card-header>
        <ion-card-title>{{record.visitType}}</ion-card-title>
        <ion-card-subtitle>{{record.visitDate | date}}</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <p><strong>Diagnosis:</strong> {{record.diagnosis}}</p>
        <p><strong>Treatment:</strong> {{record.treatment}}</p>
        <p><strong>Provider:</strong> {{record.healthcareProvider}}</p>
        <p><strong>Facility:</strong> {{record.healthcareFacility}}</p>
      </ion-card-content>
    </ion-card>

    <div *ngIf="medicalHistory.length === 0" class="empty-state">
      <ion-icon name="time-outline" size="large"></ion-icon>
      <h3>No Medical History</h3>
      <p>No medical records found</p>
    </div>
  </div>
</ion-content>
