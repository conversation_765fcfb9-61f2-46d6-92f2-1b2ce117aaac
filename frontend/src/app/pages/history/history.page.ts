import { Component, OnInit } from '@angular/core';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'app-history',
  templateUrl: './history.page.html',
  styleUrls: ['./history.page.scss'],
})
export class HistoryPage implements OnInit {
  medicalHistory: any[] = [];
  loading = true;

  constructor(private apiService: ApiService) { }

  ngOnInit() {
    this.loadHistory();
  }

  async loadHistory() {
    try {
      const response = await (await this.apiService.getMedicalHistory()).toPromise();
      this.medicalHistory = response || [];
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      this.loading = false;
    }
  }

  async doRefresh(event: any) {
    await this.loadHistory();
    event.target.complete();
  }
}
