<ion-header>
  <ion-toolbar>
    <ion-title>Allergies</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div class="content-container">
    <ion-card *ngFor="let allergy of allergies">
      <ion-card-header>
        <ion-card-title>{{allergy.allergen}}</ion-card-title>
        <ion-card-subtitle>Identified: {{allergy.dateIdentified | date}}</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <p><strong>Reaction:</strong> {{allergy.reaction}}</p>
        <ion-chip [color]="getSeverityColor(allergy.severity)">
          <ion-label>{{allergy.severity}}</ion-label>
        </ion-chip>
      </ion-card-content>
    </ion-card>

    <div *ngIf="allergies.length === 0" class="empty-state">
      <ion-icon name="warning-outline" size="large"></ion-icon>
      <h3>No Known Allergies</h3>
      <p>No allergies recorded</p>
    </div>
  </div>
</ion-content>
