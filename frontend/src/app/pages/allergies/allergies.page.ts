import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-allergies',
  templateUrl: './allergies.page.html',
  styleUrls: ['./allergies.page.scss'],
})
export class AllergiesPage implements OnInit {
  allergies: any[] = [];
  loading = false;

  constructor() { }

  ngOnInit() {
    // Mock data for now
    this.allergies = [
      {
        allergen: 'Peanuts',
        severity: 'Severe',
        reaction: 'Anaphylaxis',
        dateIdentified: '2020-01-15'
      },
      {
        allergen: 'Penicillin',
        severity: 'Moderate',
        reaction: 'Rash, itching',
        dateIdentified: '2019-06-20'
      }
    ];
  }

  getSeverityColor(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'severe': return 'danger';
      case 'moderate': return 'warning';
      case 'mild': return 'success';
      default: return 'medium';
    }
  }

  async doRefresh(event: any) {
    // Reload allergies
    event.target.complete();
  }
}
