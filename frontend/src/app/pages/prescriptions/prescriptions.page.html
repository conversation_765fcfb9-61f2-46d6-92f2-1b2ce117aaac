<ion-header>
  <ion-toolbar>
    <ion-title>Prescriptions</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div *ngIf="loading" class="loading-container">
    <ion-spinner></ion-spinner>
    <p>Loading medications...</p>
  </div>

  <div *ngIf="!loading" class="content-container">
    <ion-card *ngFor="let medication of medications">
      <ion-card-header>
        <ion-card-title>{{medication.medicationName}}</ion-card-title>
        <ion-card-subtitle>{{medication.dosage}} - {{medication.frequency}}</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <p><strong>Type:</strong> {{medication.medicationType}}</p>
        <p><strong>Duration:</strong> {{medication.duration}}</p>
        <p><strong>Prescribed by:</strong> {{medication.prescribingPhysician}}</p>
        <ion-chip [color]="medication.isActive ? 'success' : 'medium'">
          <ion-label>{{medication.isActive ? 'Active' : 'Inactive'}}</ion-label>
        </ion-chip>
      </ion-card-content>
    </ion-card>

    <div *ngIf="medications.length === 0" class="empty-state">
      <ion-icon name="medical-outline" size="large"></ion-icon>
      <h3>No Medications</h3>
      <p>No current medications found</p>
    </div>
  </div>
</ion-content>
