import { Component, OnInit } from '@angular/core';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'app-prescriptions',
  templateUrl: './prescriptions.page.html',
  styleUrls: ['./prescriptions.page.scss'],
})
export class PrescriptionsPage implements OnInit {
  medications: any[] = [];
  loading = true;

  constructor(private apiService: ApiService) { }

  ngOnInit() {
    this.loadMedications();
  }

  async loadMedications() {
    try {
      const response = await (await this.apiService.getCurrentMedications()).toPromise();
      this.medications = response || [];
    } catch (error) {
      console.error('Error loading medications:', error);
    } finally {
      this.loading = false;
    }
  }

  async doRefresh(event: any) {
    await this.loadMedications();
    event.target.complete();
  }
}
