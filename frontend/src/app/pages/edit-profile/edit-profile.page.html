<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Edit Profile / පැතිකඩ සංස්කරණය</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="saveProfile()" [disabled]="loading">
        <ion-icon name="checkmark"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="edit-profile-container">
    
    <!-- Basic Information -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="person-outline"></ion-icon>
          Basic Information / මූලික තොරතුරු
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label position="stacked">First Name / මුල් නම</ion-label>
          <ion-input [(ngModel)]="profile.firstName" placeholder="Enter first name"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Last Name / අවසන් නම</ion-label>
          <ion-input [(ngModel)]="profile.lastName" placeholder="Enter last name"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Phone Number / දුරකථන අංකය</ion-label>
          <ion-input [(ngModel)]="profile.phoneNumber" type="tel" placeholder="Enter phone number"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Date of Birth / උපන් දිනය</ion-label>
          <ion-datetime [(ngModel)]="profile.dateOfBirth" display-format="YYYY-MM-DD" picker-format="YYYY-MM-DD"></ion-datetime>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Gender / ලිංගය</ion-label>
          <ion-select [(ngModel)]="profile.gender" placeholder="Select gender">
            <ion-select-option *ngFor="let option of genderOptions" [value]="option.value">
              {{option.label}}
            </ion-select-option>
          </ion-select>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Blood Group / රුධිර කාණ්ඩය</ion-label>
          <ion-select [(ngModel)]="profile.bloodGroup" placeholder="Select blood group">
            <ion-select-option *ngFor="let group of bloodGroupOptions" [value]="group">
              {{group}}
            </ion-select-option>
          </ion-select>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Health ID / සෞඛ්‍ය හැඳුනුම්පත</ion-label>
          <ion-input [(ngModel)]="profile.healthId" placeholder="Enter health ID"></ion-input>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- Physical Measurements -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="fitness-outline"></ion-icon>
          Physical Measurements / ශාරීරික මිනුම්
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label position="stacked">Height (cm) / උස (සෙ.මී.)</ion-label>
          <ion-input [(ngModel)]="profile.height" type="number" placeholder="Enter height in cm"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Weight (kg) / බර (කි.ග්‍රෑ.)</ion-label>
          <ion-input [(ngModel)]="profile.weight" type="number" placeholder="Enter weight in kg"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label>
            <h3>BMI / ශරීර ස්කන්ධ දර්ශකය</h3>
            <p>{{calculateBMI()}} - {{getBMICategory()}}</p>
          </ion-label>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">
            Waist Circumference (cm) / ඉණ පරිධිය (සෙ.මී.)
            <p><small>Ideal: Males ≤90cm, Females ≤80cm / පරමාදර්ශී: පුරුෂයන් ≤90සෙ.මී., ගැහැණු ≤80සෙ.මී.</small></p>
          </ion-label>
          <ion-input [(ngModel)]="profile.waistCircumference" type="number" placeholder="Enter waist circumference"></ion-input>
        </ion-item>
        
        <ion-item *ngIf="profile.waistCircumference">
          <ion-label>
            <h3>Status / තත්ත්වය</h3>
            <p>{{getWaistCircumferenceStatus()}}</p>
          </ion-label>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- Health Information -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="medical-outline"></ion-icon>
          Health Information / සෞඛ්‍ය තොරතුරු
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label position="stacked">Smoking Status / දුම්පාන තත්ත්වය</ion-label>
          <ion-select [(ngModel)]="profile.smokingStatus" placeholder="Select smoking status">
            <ion-select-option *ngFor="let option of smokingStatusOptions" [value]="option.value">
              {{option.label}}
            </ion-select-option>
          </ion-select>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Physical Activity Level / ශාරීරික ක්‍රියාකාරකම් මට්ටම</ion-label>
          <ion-select [(ngModel)]="profile.physicalActivityLevel" placeholder="Select activity level">
            <ion-select-option *ngFor="let option of activityLevelOptions" [value]="option.value">
              {{option.label}}
            </ion-select-option>
          </ion-select>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">MET Score / Exercise Tolerance / MET ලකුණු / ව්‍යායාම ඉවසීම</ion-label>
          <ion-input [(ngModel)]="profile.metScore" type="number" step="0.1" placeholder="Enter MET score"></ion-input>
        </ion-item>
        
        <!-- Allergies -->
        <div class="allergies-section">
          <ion-item>
            <ion-label>
              <h3>Allergies / ආසාත්මිකතා</h3>
            </ion-label>
            <ion-button fill="clear" (click)="addAllergy()">
              <ion-icon name="add"></ion-icon>
            </ion-button>
          </ion-item>
          
          <ion-chip *ngFor="let allergy of profile.allergies; let i = index" color="warning">
            <ion-label>{{allergy}}</ion-label>
            <ion-icon name="close" (click)="removeAllergy(i)"></ion-icon>
          </ion-chip>
        </div>
        
        <!-- Past Medical Illnesses -->
        <div class="medical-conditions-section">
          <ion-item>
            <ion-label>
              <h3>Past Medical Illnesses / පෙර වෛද්‍ය රෝග</h3>
            </ion-label>
          </ion-item>
          
          <ion-item *ngFor="let condition of medicalConditions">
            <ion-checkbox 
              [checked]="isMedicalConditionSelected(condition.value)"
              (ionChange)="onMedicalConditionChange(condition.value, $event)">
            </ion-checkbox>
            <ion-label class="ion-margin-start">{{condition.label}}</ion-label>
          </ion-item>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Contact Information -->
    <ion-card>
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="call-outline"></ion-icon>
          Contact Information / සම්බන්ධතා තොරතුරු
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label position="stacked">Address / ලිපිනය</ion-label>
          <ion-textarea [(ngModel)]="profile.address" placeholder="Enter address" rows="3"></ion-textarea>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Emergency Contact Name / හදිසි සම්බන්ධතා නම</ion-label>
          <ion-input [(ngModel)]="profile.emergencyContactName" placeholder="Enter emergency contact name"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Emergency Contact Phone / හදිසි සම්බන්ධතා දුරකථනය</ion-label>
          <ion-input [(ngModel)]="profile.emergencyContactPhone" type="tel" placeholder="Enter emergency contact phone"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Emergency Contact Relation / හදිසි සම්බන්ධතා සම්බන්ධතාවය</ion-label>
          <ion-input [(ngModel)]="profile.emergencyContactRelation" placeholder="Enter relation (e.g., Spouse, Parent)"></ion-input>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- Save Button -->
    <ion-button expand="block" (click)="saveProfile()" [disabled]="loading" class="save-button">
      <ion-icon name="save-outline" slot="start"></ion-icon>
      Save Profile / පැතිකඩ සුරකින්න
    </ion-button>
  </div>
</ion-content>
