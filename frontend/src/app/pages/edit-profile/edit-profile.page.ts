import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController, LoadingController, ToastController } from '@ionic/angular';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'app-edit-profile',
  templateUrl: './edit-profile.page.html',
  styleUrls: ['./edit-profile.page.scss'],
})
export class EditProfilePage implements OnInit {
  profile: any = {
    // Basic Information
    firstName: '',
    lastName: '',
    phoneNumber: '',
    dateOfBirth: '',
    gender: '',
    bloodGroup: '',
    healthId: '',
    
    // Physical Measurements
    height: null,
    weight: null,
    waistCircumference: null,
    
    // Health Information
    allergies: [],
    smokingStatus: 'NO',
    physicalActivityLevel: '',
    metScore: null,
    pastMedicalIllnesses: [],
    
    // Contact Information
    address: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelation: ''
  };

  loading = false;
  
  // Options for dropdowns - now using language service
  bloodGroupOptions = [
    'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
  ];

  constructor(
    private apiService: ApiService,
    private languageService: LanguageService,
    private router: Router,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private toastController: ToastController
  ) {}

  ngOnInit() {
    this.loadProfile();
  }

  async loadProfile() {
    this.loading = true;
    try {
      const response = await (await this.apiService.getProfile()).toPromise();
      this.profile = { ...this.profile, ...response };
      
      // Convert date format if needed
      if (this.profile.dateOfBirth) {
        this.profile.dateOfBirth = new Date(this.profile.dateOfBirth).toISOString().split('T')[0];
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      const toast = await this.toastController.create({
        message: 'Failed to load profile data',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      this.loading = false;
    }
  }

  calculateBMI(): string {
    if (this.profile.height && this.profile.weight) {
      const heightInMeters = this.profile.height / 100;
      const bmi = this.profile.weight / (heightInMeters * heightInMeters);
      return bmi.toFixed(1);
    }
    return 'N/A';
  }

  getBMICategory(): string {
    const bmi = parseFloat(this.calculateBMI());
    if (isNaN(bmi)) return '';
    
    if (bmi < 18.5) return 'Underweight / අඩු බර';
    if (bmi < 25) return 'Normal / සාමාන්‍ය';
    if (bmi < 30) return 'Overweight / වැඩි බර';
    return 'Obese / තරබාරු';
  }

  getWaistCircumferenceStatus(): string {
    if (!this.profile.waistCircumference || !this.profile.gender) return '';
    
    const waist = this.profile.waistCircumference;
    const isIdeal = (this.profile.gender === 'MALE' && waist <= 90) || 
                   (this.profile.gender === 'FEMALE' && waist <= 80);
    
    return isIdeal ? 'Ideal / පරමාදර්ශී' : 'Above Ideal / පරමාදර්ශයට වඩා';
  }

  onMedicalConditionChange(condition: string, event: any) {
    if (event.detail.checked) {
      if (!this.profile.pastMedicalIllnesses.includes(condition)) {
        this.profile.pastMedicalIllnesses.push(condition);
      }
    } else {
      const index = this.profile.pastMedicalIllnesses.indexOf(condition);
      if (index > -1) {
        this.profile.pastMedicalIllnesses.splice(index, 1);
      }
    }
  }

  isMedicalConditionSelected(condition: string): boolean {
    return this.profile.pastMedicalIllnesses.includes(condition);
  }

  async addAllergy() {
    const alert = await this.alertController.create({
      header: 'Add Allergy / ආසාත්මිකතාවයක් එක් කරන්න',
      inputs: [
        {
          name: 'allergy',
          type: 'text',
          placeholder: 'Enter allergy / ආසාත්මිකතාවය ඇතුළත් කරන්න'
        }
      ],
      buttons: [
        {
          text: 'Cancel / අවලංගු කරන්න',
          role: 'cancel'
        },
        {
          text: 'Add / එක් කරන්න',
          handler: (data) => {
            if (data.allergy && data.allergy.trim()) {
              this.profile.allergies.push(data.allergy.trim());
            }
          }
        }
      ]
    });
    await alert.present();
  }

  removeAllergy(index: number) {
    this.profile.allergies.splice(index, 1);
  }

  async saveProfile() {
    const loading = await this.loadingController.create({
      message: 'Saving profile... / පැතිකඩ සුරකිමින්...'
    });
    await loading.present();

    try {
      await (await this.apiService.updateProfile(this.profile)).toPromise();
      
      const toast = await this.toastController.create({
        message: 'Profile updated successfully! / පැතිකඩ සාර්ථකව යාවත්කාලීන කරන ලදී!',
        duration: 3000,
        color: 'success'
      });
      await toast.present();
      
      this.router.navigate(['/tabs/profile']);
    } catch (error) {
      console.error('Error updating profile:', error);
      const toast = await this.toastController.create({
        message: 'Failed to update profile / පැතිකඩ යාවත්කාලීන කිරීම අසාර්ථක විය',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      await loading.dismiss();
    }
  }

  goBack() {
    this.router.navigate(['/tabs/profile']);
  }

  // Language service methods
  translate(key: string): string {
    return this.languageService.translate(key);
  }

  // Get translated options
  getGenderOptions() {
    return this.languageService.getGenderOptions();
  }

  getSmokingStatusOptions() {
    return this.languageService.getSmokingStatusOptions();
  }

  getActivityLevelOptions() {
    return this.languageService.getActivityLevelOptions();
  }

  getMedicalConditions() {
    return this.languageService.getMedicalConditions();
  }
}
