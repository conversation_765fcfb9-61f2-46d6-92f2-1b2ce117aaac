.edit-profile-container {
  padding: 16px;
  padding-bottom: 32px;
  max-width: 600px;
  margin: 0 auto;
}

ion-card {
  margin-bottom: 20px;
  border-radius: 16px;
  box-shadow: none;
  border: none;
  background: var(--ion-color-light, #f8f9fa);
  overflow: hidden;
}

ion-card-header {
  padding: 20px 20px 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

ion-card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;

  ion-icon {
    color: white;
    font-size: 1.4rem;
  }
}

ion-card-content {
  padding: 24px 20px;
}

ion-item {
  --background: transparent;
  --border-color: transparent;
  --inner-border-width: 0;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

ion-label {
  margin-bottom: 8px !important;

  h3 {
    color: var(--ion-color-dark, #2c3e50);
    font-weight: 600;
    margin-bottom: 6px;
    font-size: 0.95rem;
  }

  p {
    color: var(--ion-color-medium, #7f8c8d);
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.4;
  }

  small {
    color: var(--ion-color-medium-shade, #95a5a6);
    font-style: italic;
    font-size: 0.8rem;
  }
}

ion-input, ion-select, ion-textarea, ion-datetime {
  --background: white;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  border: 2px solid var(--ion-color-light-shade, #e9ecef);
  border-radius: 12px;
  margin-top: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: var(--ion-color-primary, #667eea);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

.allergies-section {
  margin-top: 20px;

  ion-item {
    margin-bottom: 16px;

    ion-label h3 {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  ion-chip {
    margin: 6px 4px 6px 0;
    --background: var(--ion-color-warning-tint, #ffc947);
    --color: var(--ion-color-warning-contrast, #000);
    border-radius: 20px;
    font-weight: 500;

    ion-icon {
      cursor: pointer;
      margin-left: 8px;
      font-size: 1rem;
    }
  }
}

.medical-conditions-section {
  margin-top: 20px;

  > ion-item {
    margin-bottom: 16px;
  }

  ion-item {
    margin-bottom: 12px;
    padding: 8px 0;

    ion-checkbox {
      margin-right: 16px;
      --size: 20px;
      --checkmark-color: white;
      --background-checked: var(--ion-color-primary, #667eea);
      --border-color-checked: var(--ion-color-primary, #667eea);
    }

    ion-label {
      font-size: 0.95rem;
      color: var(--ion-color-dark, #2c3e50);
      line-height: 1.4;
    }
  }
}

.save-button {
  margin: 32px 16px 16px 16px;
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --background-activated: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  --color: white;
  height: 56px;
  font-weight: 600;
  border-radius: 16px;
  font-size: 1.1rem;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);

  ion-icon {
    font-size: 1.2rem;
  }
}

// BMI and status indicators
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  
  &.normal {
    color: #27ae60;
  }
  
  &.warning {
    color: #f39c12;
  }
  
  &.danger {
    color: #e74c3c;
  }
}

// Responsive design
@media (max-width: 768px) {
  .edit-profile-container {
    padding: 12px;
  }
  
  ion-card-title {
    font-size: 1rem;
  }
  
  ion-item ion-label h3 {
    font-size: 0.85rem;
  }
}

// Loading state
ion-content.loading {
  opacity: 0.6;
  pointer-events: none;
}

// Form validation styles
.invalid-input {
  --border-color: #e74c3c !important;
  border-color: #e74c3c !important;
}

.validation-error {
  color: #e74c3c;
  font-size: 0.75rem;
  margin-top: 4px;
  margin-left: 12px;
}

// Accessibility improvements
ion-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  ion-card {
    --background: #2c3e50;
  }
  
  ion-card-title {
    color: #ecf0f1;
  }
  
  ion-item ion-label h3 {
    color: #ecf0f1;
  }
  
  ion-item ion-label p {
    color: #bdc3c7;
  }
  
  ion-input, ion-select, ion-textarea, ion-datetime {
    --background: #34495e;
    --color: #ecf0f1;
    border-color: #4a5f7a;
  }
}
