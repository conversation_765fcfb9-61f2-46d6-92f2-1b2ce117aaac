.edit-profile-container {
  padding: 16px;
  padding-bottom: 32px;
}

ion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

ion-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2c3e50;
  font-size: 1.1rem;
  
  ion-icon {
    color: #e74c3c;
  }
}

ion-item {
  --border-color: transparent;
  --inner-border-width: 0;
  margin-bottom: 8px;
  
  ion-label {
    margin-bottom: 8px;
    
    h3 {
      color: #2c3e50;
      font-weight: 600;
      margin-bottom: 4px;
      font-size: 0.9rem;
    }
    
    p {
      color: #7f8c8d;
      margin: 0;
      font-size: 0.8rem;
    }
    
    small {
      color: #95a5a6;
      font-style: italic;
    }
  }
  
  ion-input, ion-select, ion-textarea, ion-datetime {
    --background: #f8f9fa;
    --border-radius: 8px;
    --padding-start: 12px;
    --padding-end: 12px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
  }
  
  ion-input:focus-within,
  ion-select:focus-within,
  ion-textarea:focus-within {
    --border-color: #e74c3c;
    border-color: #e74c3c;
  }
}

.allergies-section {
  margin-top: 16px;
  
  ion-chip {
    margin: 4px;
    
    ion-icon {
      cursor: pointer;
      margin-left: 8px;
    }
  }
}

.medical-conditions-section {
  margin-top: 16px;
  
  ion-item {
    margin-bottom: 4px;
    
    ion-checkbox {
      margin-right: 12px;
    }
    
    ion-label {
      font-size: 0.9rem;
      color: #2c3e50;
    }
  }
}

.save-button {
  margin: 24px 0;
  --background: #e74c3c;
  --background-activated: #c0392b;
  --color: white;
  height: 48px;
  font-weight: 600;
  border-radius: 12px;
}

// BMI and status indicators
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  
  &.normal {
    color: #27ae60;
  }
  
  &.warning {
    color: #f39c12;
  }
  
  &.danger {
    color: #e74c3c;
  }
}

// Responsive design
@media (max-width: 768px) {
  .edit-profile-container {
    padding: 12px;
  }
  
  ion-card-title {
    font-size: 1rem;
  }
  
  ion-item ion-label h3 {
    font-size: 0.85rem;
  }
}

// Loading state
ion-content.loading {
  opacity: 0.6;
  pointer-events: none;
}

// Form validation styles
.invalid-input {
  --border-color: #e74c3c !important;
  border-color: #e74c3c !important;
}

.validation-error {
  color: #e74c3c;
  font-size: 0.75rem;
  margin-top: 4px;
  margin-left: 12px;
}

// Accessibility improvements
ion-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  ion-card {
    --background: #2c3e50;
  }
  
  ion-card-title {
    color: #ecf0f1;
  }
  
  ion-item ion-label h3 {
    color: #ecf0f1;
  }
  
  ion-item ion-label p {
    color: #bdc3c7;
  }
  
  ion-input, ion-select, ion-textarea, ion-datetime {
    --background: #34495e;
    --color: #ecf0f1;
    border-color: #4a5f7a;
  }
}
