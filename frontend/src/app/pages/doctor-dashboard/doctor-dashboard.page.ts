import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Alert<PERSON>ontroller, LoadingController, ToastController } from '@ionic/angular';
import { AuthService, User } from '../../services/auth.service';
import { ApiService } from '../../services/api.service';
import { LanguageService } from '../../services/language.service';

@Component({
  selector: 'app-doctor-dashboard',
  templateUrl: './doctor-dashboard.page.html',
  styleUrls: ['./doctor-dashboard.page.scss'],
})
export class DoctorDashboardPage implements OnInit {
  patients: any[] = [];
  user: User | null = null;
  loading = true;
  searchTerm: string = '';
  filteredPatients: any[] = [];

  constructor(
    private authService: AuthService,
    private apiService: ApiService,
    private languageService: LanguageService,
    private router: Router,
    private alertController: AlertController,
    private loadingController: Loading<PERSON>ontroller,
    private toastController: ToastController
  ) {}

  ngOnInit() {
    this.authService.user$.subscribe(user => {
      this.user = user;
      if (user && user.role === 'DOCTOR') {
        this.loadMyPatients();
      }
    });
  }

  async loadMyPatients() {
    if (!this.user) return;
    
    try {
      const response = await (await this.apiService.getPatientsByDoctor(this.user.id)).toPromise();
      this.patients = response || [];
      this.filteredPatients = [...this.patients];
    } catch (error) {
      console.error('Error loading patients:', error);
      const toast = await this.toastController.create({
        message: 'Failed to load patients',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      this.loading = false;
    }
  }

  filterPatients() {
    if (!this.searchTerm.trim()) {
      this.filteredPatients = [...this.patients];
    } else {
      const term = this.searchTerm.toLowerCase();
      this.filteredPatients = this.patients.filter(patient => 
        patient.user?.firstName?.toLowerCase().includes(term) ||
        patient.user?.lastName?.toLowerCase().includes(term) ||
        patient.user?.email?.toLowerCase().includes(term) ||
        patient.healthId?.toLowerCase().includes(term) ||
        patient.phoneNumber?.includes(term)
      );
    }
  }

  async viewPatientProfile(patient: any) {
    // Navigate to patient profile view
    this.router.navigate(['/patient-profile', patient.id]);
  }

  getPatientDisplayName(patient: any): string {
    if (patient.user) {
      return `${patient.user.firstName} ${patient.user.lastName}`;
    }
    return 'Unknown Patient';
  }

  getPatientAge(patient: any): string {
    if (!patient.dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(patient.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age.toString();
  }

  getPatientGender(patient: any): string {
    return patient.gender || 'Not specified';
  }

  getPatientHealthId(patient: any): string {
    return patient.healthId || 'Not assigned';
  }

  hasRecentActivity(patient: any): boolean {
    // Check if patient has recent vital signs, lab results, or medical records
    // This would need to be implemented based on your data structure
    return false; // Placeholder
  }

  async doRefresh(event: any) {
    await this.loadMyPatients();
    event.target.complete();
  }

  async handleLogout() {
    const alert = await this.alertController.create({
      header: 'Logout',
      message: 'Are you sure you want to logout?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Logout',
          role: 'destructive',
          handler: async () => {
            await this.authService.logout();
            this.router.navigate(['/login']);
          }
        }
      ]
    });
    await alert.present();
  }

  // Language service methods
  translate(key: string): string {
    return this.languageService.translate(key);
  }
}
