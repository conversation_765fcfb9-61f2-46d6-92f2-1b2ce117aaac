<ion-header>
  <ion-toolbar>
    <ion-title>Doctor Dashboard</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="handleLogout()">
        <ion-icon name="log-out-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div class="dashboard-container">
    <!-- Welcome Header -->
    <ion-card class="header-card">
      <ion-card-content>
        <div class="doctor-header">
          <div class="doctor-avatar">
            <ion-icon name="medical" size="large"></ion-icon>
          </div>
          <div class="doctor-info">
            <h2>Dr. {{user?.username}}</h2>
            <p>{{user?.email}}</p>
            <ion-chip color="primary">
              <ion-label>{{user?.role}}</ion-label>
            </ion-chip>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Search Bar -->
    <ion-searchbar 
      [(ngModel)]="searchTerm" 
      (ionInput)="filterPatients()"
      placeholder="Search patients..."
      show-clear-button="focus">
    </ion-searchbar>

    <!-- Loading -->
    <div *ngIf="loading" class="loading-container">
      <ion-spinner></ion-spinner>
      <p>Loading patients...</p>
    </div>

    <!-- Patients List -->
    <div *ngIf="!loading">
      <!-- Patient Count -->
      <ion-card *ngIf="patients.length > 0">
        <ion-card-header>
          <ion-card-title>
            <ion-icon name="people-outline"></ion-icon>
            My Patients ({{filteredPatients.length}})
          </ion-card-title>
        </ion-card-header>
      </ion-card>

      <!-- Patient Cards -->
      <ion-card *ngFor="let patient of filteredPatients" 
               class="patient-card" 
               (click)="viewPatientProfile(patient)">
        <ion-card-content>
          <div class="patient-info">
            <div class="patient-avatar">
              <ion-icon name="person-circle" size="large" color="primary"></ion-icon>
            </div>
            <div class="patient-details">
              <h3>{{getPatientDisplayName(patient)}}</h3>
              <div class="patient-meta">
                <p class="patient-age">
                  <ion-icon name="calendar-outline"></ion-icon>
                  {{getPatientAge(patient)}} years old
                </p>
                <p class="patient-gender">
                  <ion-icon name="person-outline"></ion-icon>
                  {{getPatientGender(patient)}}
                </p>
                <p class="patient-id" *ngIf="getPatientHealthId(patient) !== 'Not assigned'">
                  <ion-icon name="card-outline"></ion-icon>
                  {{getPatientHealthId(patient)}}
                </p>
                <p class="patient-contact" *ngIf="patient.phoneNumber">
                  <ion-icon name="call-outline"></ion-icon>
                  {{patient.phoneNumber}}
                </p>
              </div>
              <div class="patient-status" *ngIf="hasRecentActivity(patient)">
                <ion-chip color="success" outline>
                  <ion-icon name="pulse-outline"></ion-icon>
                  <ion-label>Recent Activity</ion-label>
                </ion-chip>
              </div>
            </div>
            <div class="view-indicator">
              <ion-icon name="chevron-forward-outline" color="medium"></ion-icon>
            </div>
          </div>
        </ion-card-content>
      </ion-card>

      <!-- No Patients Found -->
      <div *ngIf="filteredPatients.length === 0 && patients.length > 0" class="empty-state">
        <ion-icon name="search-outline" size="large"></ion-icon>
        <h3>No patients found</h3>
        <p>Try adjusting your search criteria</p>
      </div>

      <!-- No Patients at All -->
      <div *ngIf="patients.length === 0" class="empty-state">
        <ion-icon name="people-outline" size="large"></ion-icon>
        <h3>No patients yet</h3>
        <p>Patients who select you as their doctor will appear here</p>
      </div>
    </div>
  </div>
</ion-content>
