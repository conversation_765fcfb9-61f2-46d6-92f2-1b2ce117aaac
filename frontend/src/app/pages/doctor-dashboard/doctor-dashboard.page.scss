// Doctor dashboard uses global styles, only specific overrides here

.dashboard-container {
  padding: 16px;
  padding-bottom: 32px;
  max-width: 800px;
  margin: 0 auto;
}

.header-card {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  color: white;
  margin-bottom: 20px;
  border-radius: var(--card-border-radius) !important;
  box-shadow: var(--card-shadow) !important;
  
  ion-card-content {
    padding: var(--card-padding);
  }
}

.doctor-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.doctor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255, 255, 255, 0.2);
  
  ion-icon {
    font-size: 3rem;
    color: white;
  }
}

.doctor-info {
  flex: 1;
  
  h2 {
    margin: 0 0 8px 0;
    color: white;
    font-weight: var(--heading-font-weight);
    font-size: 1.4rem;
  }
  
  p {
    margin: 0 0 12px 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
  }
  
  ion-chip {
    --background: rgba(255, 255, 255, 0.2);
    --color: white;
    font-weight: 600;
    border-radius: 20px;
  }
}

.patient-card {
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

.patient-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  position: relative;
}

.patient-avatar {
  flex-shrink: 0;
  
  ion-icon {
    font-size: 3rem;
  }
}

.patient-details {
  flex: 1;
  
  h3 {
    margin: 0 0 12px 0;
    color: var(--ion-color-dark);
    font-weight: 600;
    font-size: 1.1rem;
  }
}

.patient-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
  
  p {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    color: var(--ion-color-medium);
    font-size: 0.9rem;
    
    ion-icon {
      font-size: 0.9rem;
      flex-shrink: 0;
    }
  }
}

.patient-status {
  margin-top: 8px;
  
  ion-chip {
    font-size: 0.8rem;
    height: 28px;
    
    ion-icon {
      font-size: 0.8rem;
      margin-right: 4px;
    }
  }
}

.view-indicator {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  
  ion-icon {
    font-size: 1.2rem;
  }
}

// Search bar styling
ion-searchbar {
  --background: var(--card-background);
  --border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: var(--card-shadow);
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .patient-info {
    gap: 12px;
  }
  
  .patient-avatar ion-icon {
    font-size: 2.5rem;
  }
  
  .patient-details h3 {
    font-size: 1rem;
  }
  
  .patient-meta {
    gap: 4px;
    
    p {
      font-size: 0.85rem;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .header-card {
    background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  }
  
  .patient-card {
    --background: #2c3e50;
  }
  
  .patient-details h3 {
    color: #ecf0f1;
  }
  
  .patient-meta p {
    color: #bdc3c7;
  }
}
