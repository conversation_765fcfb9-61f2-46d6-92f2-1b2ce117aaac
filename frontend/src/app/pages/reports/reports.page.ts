import { Component, OnInit } from '@angular/core';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.page.html',
  styleUrls: ['./reports.page.scss'],
})
export class ReportsPage implements OnInit {
  vitalSigns: any = null;
  labResults: any = null;
  loading = true;

  constructor(private apiService: ApiService) { }

  ngOnInit() {
    this.loadReports();
  }

  async loadReports() {
    try {
      const [vitalsResponse, labsResponse] = await Promise.all([
        (await this.apiService.getCurrentVitalSigns()).toPromise(),
        (await this.apiService.getCurrentLabResults()).toPromise()
      ]);
      this.vitalSigns = vitalsResponse;
      this.labResults = labsResponse;
    } catch (error) {
      console.error('Error loading reports:', error);
    } finally {
      this.loading = false;
    }
  }

  async doRefresh(event: any) {
    await this.loadReports();
    event.target.complete();
  }
}
