<ion-header>
  <ion-toolbar>
    <ion-title>Health Reports</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div *ngIf="loading" class="loading-container">
    <ion-spinner></ion-spinner>
    <p>Loading reports...</p>
  </div>

  <div *ngIf="!loading" class="content-container">
    <!-- Vital Signs -->
    <ion-card *ngIf="vitalSigns">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="heart-outline"></ion-icon>
          Current Vital Signs
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-label>
              <h3>Blood Pressure</h3>
              <p>{{vitalSigns.systolicBP}}/{{vitalSigns.diastolicBP}} mmHg</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Pulse Rate</h3>
              <p>{{vitalSigns.pulseRate}} bpm</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Respiratory Rate</h3>
              <p>{{vitalSigns.respiratoryRate}} breaths/min</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Lab Results -->
    <ion-card *ngIf="labResults">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="flask-outline"></ion-icon>
          Current Lab Results
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-label>
              <h3>Blood Glucose</h3>
              <p>{{labResults.bloodGlucoseLevel}} mg/dL</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Hemoglobin</h3>
              <p>{{labResults.hemoglobinLevel}} g/dL</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>LDL Cholesterol</h3>
              <p>{{labResults.ldlCholesterolLevel}} mg/dL</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Serum Creatinine</h3>
              <p>{{labResults.serumCreatinineLevel}} mg/dL</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <div *ngIf="!vitalSigns && !labResults" class="empty-state">
      <ion-icon name="analytics-outline" size="large"></ion-icon>
      <h3>No Reports Available</h3>
      <p>No health reports found</p>
    </div>
  </div>
</ion-content>
