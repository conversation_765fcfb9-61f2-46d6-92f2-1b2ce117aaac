.login-content {
  --background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.login-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.logo-container {
  text-align: center;
  margin-bottom: 40px;
}

.logo-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

  ion-icon {
    color: #e74c3c;
    font-size: 60px;
  }
}

.app-title {
  font-size: 32px;
  font-weight: bold;
  color: white;
  margin: 0 0 5px 0;
}

.app-subtitle {
  font-size: 16px;
  color: white;
  opacity: 0.9;
  margin: 0;
}

.form-container {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.welcome-text {
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
  text-align: center;
  margin: 0 0 5px 0;
}

.subtitle-text {
  font-size: 16px;
  color: #7f8c8d;
  text-align: center;
  margin: 0 0 30px 0;
}

.input-item {
  --border-radius: 12px;
  --background: #f8f9fa;
  --border-color: #ecf0f1;
  --border-width: 1px;
  margin-bottom: 20px;
  --min-height: 50px;
}

.input-icon {
  color: #95a5a6;
  margin-right: 10px;
}

.eye-button {
  --color: #95a5a6;
  margin: 0;
}

.login-button {
  --background: #e74c3c;
  --border-radius: 12px;
  height: 50px;
  margin: 10px 0 20px 0;
  font-weight: bold;
  font-size: 18px;
}

.login-button[disabled] {
  --background: #bdc3c7;
}

.signup-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.signup-text {
  font-size: 16px;
  color: #7f8c8d;
}

.signup-link {
  --color: #e74c3c;
  font-weight: bold;
  font-size: 16px;
  margin: 0;
}
