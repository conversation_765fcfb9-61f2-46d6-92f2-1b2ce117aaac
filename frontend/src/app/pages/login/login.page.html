<ion-content class="login-content">
  <div class="login-container">
    <!-- Logo Section -->
    <div class="logo-container">
      <div class="logo-circle">
        <ion-icon name="medical" size="large"></ion-icon>
      </div>
      <h1 class="app-title">HealthRecord</h1>
      <p class="app-subtitle">Your Digital Health Companion</p>
    </div>

    <!-- Login Form -->
    <div class="form-container">
      <h2 class="welcome-text">Welcome Back</h2>
      <p class="subtitle-text">Sign in to access your health records</p>

      <form [formGroup]="loginForm" (ngSubmit)="onLogin()">
        <!-- Username Input -->
        <ion-item class="input-item">
          <ion-icon name="person-outline" slot="start" class="input-icon"></ion-icon>
          <ion-input
            type="text"
            placeholder="Username"
            formControlName="username"
            autocapitalize="none"
            autocorrect="false">
          </ion-input>
        </ion-item>

        <!-- Password Input -->
        <ion-item class="input-item">
          <ion-icon name="lock-closed-outline" slot="start" class="input-icon"></ion-icon>
          <ion-input
            [type]="showPassword ? 'text' : 'password'"
            placeholder="Password"
            formControlName="password"
            autocapitalize="none"
            autocorrect="false">
          </ion-input>
          <ion-button
            fill="clear"
            slot="end"
            (click)="togglePasswordVisibility()"
            class="eye-button">
            <ion-icon [name]="showPassword ? 'eye-outline' : 'eye-off-outline'"></ion-icon>
          </ion-button>
        </ion-item>

        <!-- Login Button -->
        <ion-button
          expand="block"
          type="submit"
          class="login-button"
          [disabled]="!loginForm.valid">
          Sign In
        </ion-button>
      </form>

      <!-- Signup Link -->
      <div class="signup-container">
        <span class="signup-text">Don't have an account? </span>
        <ion-button fill="clear" (click)="goToSignup()" class="signup-link">
          Sign Up
        </ion-button>
      </div>
    </div>
  </div>
</ion-content>
