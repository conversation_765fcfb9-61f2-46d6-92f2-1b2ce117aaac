<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/login"></ion-back-button>
    </ion-buttons>
    <ion-title>Sign Up</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="signup-content">
  <div class="signup-container">
    <div class="header">
      <h1 class="title">Create Account</h1>
      <p class="subtitle">Join <PERSON>Record to manage your health</p>
    </div>

    <form [formGroup]="signupForm" (ngSubmit)="onSignup()">
      <!-- First Name -->
      <ion-item class="input-item">
        <ion-icon name="person-outline" slot="start" class="input-icon"></ion-icon>
        <ion-input
          type="text"
          placeholder="First Name"
          formControlName="firstName"
          autocapitalize="words">
        </ion-input>
      </ion-item>

      <!-- Last Name -->
      <ion-item class="input-item">
        <ion-icon name="person-outline" slot="start" class="input-icon"></ion-icon>
        <ion-input
          type="text"
          placeholder="Last Name"
          formControlName="lastName"
          autocapitalize="words">
        </ion-input>
      </ion-item>

      <!-- Username -->
      <ion-item class="input-item">
        <ion-icon name="at-outline" slot="start" class="input-icon"></ion-icon>
        <ion-input
          type="text"
          placeholder="Username"
          formControlName="username"
          autocapitalize="none"
          autocorrect="false">
        </ion-input>
      </ion-item>

      <!-- Email -->
      <ion-item class="input-item">
        <ion-icon name="mail-outline" slot="start" class="input-icon"></ion-icon>
        <ion-input
          type="email"
          placeholder="Email"
          formControlName="email"
          autocapitalize="none"
          autocorrect="false">
        </ion-input>
      </ion-item>

      <!-- Password -->
      <ion-item class="input-item">
        <ion-icon name="lock-closed-outline" slot="start" class="input-icon"></ion-icon>
        <ion-input
          [type]="showPassword ? 'text' : 'password'"
          placeholder="Password"
          formControlName="password"
          autocapitalize="none"
          autocorrect="false">
        </ion-input>
        <ion-button
          fill="clear"
          slot="end"
          (click)="togglePasswordVisibility()"
          class="eye-button">
          <ion-icon [name]="showPassword ? 'eye-outline' : 'eye-off-outline'"></ion-icon>
        </ion-button>
      </ion-item>

      <!-- Confirm Password -->
      <ion-item class="input-item">
        <ion-icon name="lock-closed-outline" slot="start" class="input-icon"></ion-icon>
        <ion-input
          [type]="showConfirmPassword ? 'text' : 'password'"
          placeholder="Confirm Password"
          formControlName="confirmPassword"
          autocapitalize="none"
          autocorrect="false">
        </ion-input>
        <ion-button
          fill="clear"
          slot="end"
          (click)="toggleConfirmPasswordVisibility()"
          class="eye-button">
          <ion-icon [name]="showConfirmPassword ? 'eye-outline' : 'eye-off-outline'"></ion-icon>
        </ion-button>
      </ion-item>

      <!-- Signup Button -->
      <ion-button
        expand="block"
        type="submit"
        class="signup-button"
        [disabled]="!signupForm.valid">
        Create Account
      </ion-button>
    </form>

    <!-- Login Link -->
    <div class="login-container">
      <span class="login-text">Already have an account? </span>
      <ion-button fill="clear" (click)="goToLogin()" class="login-link">
        Sign In
      </ion-button>
    </div>
  </div>
</ion-content>
