import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Preferences } from '@capacitor/preferences';

export interface Translation {
  [key: string]: string;
}

@Injectable({
  providedIn: 'root'
})
export class LanguageService {
  private currentLanguageSubject = new BehaviorSubject<string>('en');
  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  private translations: { [lang: string]: Translation } = {
    en: {
      // Navigation
      'nav.profile': 'Profile',
      'nav.prescriptions': 'Prescriptions',
      'nav.history': 'History',
      'nav.reports': 'Reports',
      'nav.allergies': 'Allergies',
      
      // Profile
      'profile.title': 'Profile',
      'profile.edit': 'Edit Profile',
      'profile.basic_info': 'Basic Information',
      'profile.physical_measurements': 'Physical Measurements',
      'profile.health_info': 'Health Information',
      'profile.contact_info': 'Contact Information',
      'profile.selected_doctor': 'Selected Doctor',
      'profile.select_doctor': 'Select Doctor',
      'profile.no_doctor_selected': 'No doctor selected',
      
      // Form Fields
      'field.first_name': 'First Name',
      'field.last_name': 'Last Name',
      'field.phone_number': 'Phone Number',
      'field.date_of_birth': 'Date of Birth',
      'field.gender': 'Gender',
      'field.blood_group': 'Blood Group',
      'field.health_id': 'Health ID',
      'field.height': 'Height (cm)',
      'field.weight': 'Weight (kg)',
      'field.waist_circumference': 'Waist Circumference (cm)',
      'field.smoking_status': 'Smoking Status',
      'field.activity_level': 'Physical Activity Level',
      'field.met_score': 'MET Score / Exercise Tolerance',
      'field.allergies': 'Allergies',
      'field.medical_conditions': 'Past Medical Illnesses',
      'field.address': 'Address',
      'field.emergency_contact': 'Emergency Contact Name',
      'field.emergency_phone': 'Emergency Contact Phone',
      'field.emergency_relation': 'Emergency Contact Relation',
      
      // Options
      'gender.male': 'Male',
      'gender.female': 'Female',
      'gender.other': 'Other',
      'smoking.no': 'No',
      'smoking.yes': 'Yes',
      'smoking.former': 'Former Smoker',
      'activity.sedentary': 'Sedentary',
      'activity.lightly_active': 'Lightly Active',
      'activity.moderately_active': 'Moderately Active',
      'activity.very_active': 'Very Active',
      'activity.extremely_active': 'Extremely Active',
      
      // Medical Conditions
      'condition.dm': 'Diabetes Mellitus',
      'condition.htn': 'Hypertension',
      'condition.dl': 'Dyslipidemia',
      'condition.ckd': 'Chronic Kidney Disease',
      'condition.stroke': 'Stroke',
      'condition.ihd': 'Ischemic Heart Disease',
      
      // BMI Categories
      'bmi.underweight': 'Underweight',
      'bmi.normal': 'Normal',
      'bmi.overweight': 'Overweight',
      'bmi.obese': 'Obese',
      
      // Status
      'status.ideal': 'Ideal',
      'status.above_ideal': 'Above Ideal',
      
      // Actions
      'action.save': 'Save',
      'action.cancel': 'Cancel',
      'action.add': 'Add',
      'action.edit': 'Edit',
      'action.delete': 'Delete',
      'action.select': 'Select',
      'action.back': 'Back',
      
      // Messages
      'message.saving': 'Saving...',
      'message.loading': 'Loading...',
      'message.success': 'Success!',
      'message.error': 'Error occurred',
      'message.profile_updated': 'Profile updated successfully!',
      'message.doctor_selected': 'Doctor selected successfully!',
      
      // Doctor Selection
      'doctor.select_title': 'Select Your Doctor',
      'doctor.search_placeholder': 'Search doctors...',
      'doctor.specialization': 'Specialization',
      'doctor.experience': 'Experience',
      'doctor.no_doctors': 'No doctors available',
      
      // Language
      'language.switch': 'Language',
      'language.english': 'English',
      'language.sinhala': 'සිංහල'
    },
    
    si: {
      // Navigation
      'nav.profile': 'පැතිකඩ',
      'nav.prescriptions': 'බෙහෙත් වට්ටෝරු',
      'nav.history': 'ඉතිහාසය',
      'nav.reports': 'වාර්තා',
      'nav.allergies': 'ආසාත්මිකතා',
      
      // Profile
      'profile.title': 'පැතිකඩ',
      'profile.edit': 'පැතිකඩ සංස්කරණය',
      'profile.basic_info': 'මූලික තොරතුරු',
      'profile.physical_measurements': 'ශාරීරික මිනුම්',
      'profile.health_info': 'සෞඛ්‍ය තොරතුරු',
      'profile.contact_info': 'සම්බන්ධතා තොරතුරු',
      'profile.selected_doctor': 'තෝරාගත් වෛද්‍යවරයා',
      'profile.select_doctor': 'වෛද්‍යවරයෙකු තෝරන්න',
      'profile.no_doctor_selected': 'වෛද්‍යවරයෙකු තෝරාගෙන නැත',
      
      // Form Fields
      'field.first_name': 'මුල් නම',
      'field.last_name': 'අවසන් නම',
      'field.phone_number': 'දුරකථන අංකය',
      'field.date_of_birth': 'උපන් දිනය',
      'field.gender': 'ලිංගය',
      'field.blood_group': 'රුධිර කාණ්ඩය',
      'field.health_id': 'සෞඛ්‍ය හැඳුනුම්පත',
      'field.height': 'උස (සෙ.මී.)',
      'field.weight': 'බර (කි.ග්‍රෑ.)',
      'field.waist_circumference': 'ඉණ පරිධිය (සෙ.මී.)',
      'field.smoking_status': 'දුම්පාන තත්ත්වය',
      'field.activity_level': 'ශාරීරික ක්‍රියාකාරකම් මට්ටම',
      'field.met_score': 'MET ලකුණු / ව්‍යායාම ඉවසීම',
      'field.allergies': 'ආසාත්මිකතා',
      'field.medical_conditions': 'පෙර වෛද්‍ය රෝග',
      'field.address': 'ලිපිනය',
      'field.emergency_contact': 'හදිසි සම්බන්ධතා නම',
      'field.emergency_phone': 'හදිසි සම්බන්ධතා දුරකථනය',
      'field.emergency_relation': 'හදිසි සම්බන්ධතා සම්බන්ධතාවය',
      
      // Options
      'gender.male': 'පුරුෂ',
      'gender.female': 'ගැහැණු',
      'gender.other': 'වෙනත්',
      'smoking.no': 'නැත',
      'smoking.yes': 'ඔව්',
      'smoking.former': 'පෙර දුම්පානකරු',
      'activity.sedentary': 'අක්‍රිය',
      'activity.lightly_active': 'සැහැල්ලු ක්‍රියාකාරී',
      'activity.moderately_active': 'මධ්‍යම ක්‍රියාකාරී',
      'activity.very_active': 'ඉතා ක්‍රියාකාරී',
      'activity.extremely_active': 'අතිශය ක්‍රියාකාරී',
      
      // Medical Conditions
      'condition.dm': 'දියවැඩියාව',
      'condition.htn': 'අධි රුධිර පීඩනය',
      'condition.dl': 'ලිපිඩ් අක්‍රමිකතා',
      'condition.ckd': 'නිදන්ගත වකුගඩු රෝගය',
      'condition.stroke': 'ආඝාතය',
      'condition.ihd': 'ඉස්කිමික් හෘද රෝගය',
      
      // BMI Categories
      'bmi.underweight': 'අඩු බර',
      'bmi.normal': 'සාමාන්‍ය',
      'bmi.overweight': 'වැඩි බර',
      'bmi.obese': 'තරබාරු',
      
      // Status
      'status.ideal': 'පරමාදර්ශී',
      'status.above_ideal': 'පරමාදර්ශයට වඩා',
      
      // Actions
      'action.save': 'සුරකින්න',
      'action.cancel': 'අවලංගු කරන්න',
      'action.add': 'එක් කරන්න',
      'action.edit': 'සංස්කරණය',
      'action.delete': 'මකන්න',
      'action.select': 'තෝරන්න',
      'action.back': 'ආපසු',
      
      // Messages
      'message.saving': 'සුරකිමින්...',
      'message.loading': 'පූරණය වෙමින්...',
      'message.success': 'සාර්ථකයි!',
      'message.error': 'දෝෂයක් සිදුවිය',
      'message.profile_updated': 'පැතිකඩ සාර්ථකව යාවත්කාලීන කරන ලදී!',
      'message.doctor_selected': 'වෛද්‍යවරයා සාර්ථකව තෝරාගන්නා ලදී!',
      
      // Doctor Selection
      'doctor.select_title': 'ඔබේ වෛද්‍යවරයා තෝරන්න',
      'doctor.search_placeholder': 'වෛද්‍යවරුන් සොයන්න...',
      'doctor.specialization': 'විශේෂඥතාව',
      'doctor.experience': 'අත්දැකීම්',
      'doctor.no_doctors': 'වෛද්‍යවරුන් නොමැත',
      
      // Language
      'language.switch': 'භාෂාව',
      'language.english': 'English',
      'language.sinhala': 'සිංහල'
    }
  };

  constructor() {
    this.loadSavedLanguage();
  }

  private async loadSavedLanguage() {
    try {
      const { value: savedLang } = await Preferences.get({ key: 'language' });
      if (savedLang && (savedLang === 'en' || savedLang === 'si')) {
        this.currentLanguageSubject.next(savedLang);
      }
    } catch (error) {
      console.error('Error loading saved language:', error);
    }
  }

  async setLanguage(lang: string) {
    if (lang === 'en' || lang === 'si') {
      this.currentLanguageSubject.next(lang);
      await Preferences.set({ key: 'language', value: lang });
    }
  }

  getCurrentLanguage(): string {
    return this.currentLanguageSubject.value;
  }

  translate(key: string): string {
    const currentLang = this.getCurrentLanguage();
    return this.translations[currentLang]?.[key] || key;
  }

  // Helper method to get translated options for dropdowns
  getGenderOptions() {
    return [
      { value: 'MALE', label: this.translate('gender.male') },
      { value: 'FEMALE', label: this.translate('gender.female') },
      { value: 'OTHER', label: this.translate('gender.other') }
    ];
  }

  getSmokingStatusOptions() {
    return [
      { value: 'NO', label: this.translate('smoking.no') },
      { value: 'YES', label: this.translate('smoking.yes') },
      { value: 'FORMER', label: this.translate('smoking.former') }
    ];
  }

  getActivityLevelOptions() {
    return [
      { value: 'SEDENTARY', label: this.translate('activity.sedentary') },
      { value: 'LIGHTLY_ACTIVE', label: this.translate('activity.lightly_active') },
      { value: 'MODERATELY_ACTIVE', label: this.translate('activity.moderately_active') },
      { value: 'VERY_ACTIVE', label: this.translate('activity.very_active') },
      { value: 'EXTREMELY_ACTIVE', label: this.translate('activity.extremely_active') }
    ];
  }

  getMedicalConditions() {
    return [
      { value: 'DM', label: this.translate('condition.dm') },
      { value: 'HTN', label: this.translate('condition.htn') },
      { value: 'DL', label: this.translate('condition.dl') },
      { value: 'CKD', label: this.translate('condition.ckd') },
      { value: 'Stroke', label: this.translate('condition.stroke') },
      { value: 'IHD', label: this.translate('condition.ihd') }
    ];
  }
}
