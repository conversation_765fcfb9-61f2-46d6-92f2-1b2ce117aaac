import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { Preferences } from '@capacitor/preferences';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private baseUrl = environment.production 
    ? 'https://service2.viganana.com/doc-file-service' 
    : 'http://localhost:8080/api';

  constructor(
    private http: HttpClient
  ) {}

  private async getAuthHeaders(): Promise<HttpHeaders> {
    const { value: token } = await Preferences.get({ key: 'token' });
    let headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  // Auth API
  async login(username: string, password: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.post(`${this.baseUrl}/auth/signin`, { username, password }, { headers });
  }

  async signup(userData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.post(`${this.baseUrl}/auth/signup`, userData, { headers });
  }

  async refreshToken(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.post(`${this.baseUrl}/auth/refresh`, {}, { headers });
  }

  // Patient API
  async getProfile(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/profile`, { headers });
  }

  async updateProfile(profileData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.put(`${this.baseUrl}/patients/profile`, profileData, { headers });
  }

  async getCurrentVitalSigns(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/profile/vitals/current`, { headers });
  }

  async getCurrentLabResults(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/profile/labs/current`, { headers });
  }

  async getCurrentMedications(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/profile/medications/current`, { headers });
  }

  async getMedicalHistory(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/profile/history`, { headers });
  }

  async getPatientById(id: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/${id}`, { headers });
  }

  async getAllPatients(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients`, { headers });
  }

  async getPatientsByDoctor(doctorId: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/by-doctor/${doctorId}`, { headers });
  }

  // Doctor selection API
  async getAllDoctors(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/doctors`, { headers });
  }

  async getSelectedDoctor(): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/patients/profile/selected-doctor`, { headers });
  }

  // Vital Signs API
  async createVitalSigns(vitalSignsData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.post(`${this.baseUrl}/vital-signs`, vitalSignsData, { headers });
  }

  async getVitalSignsByPatient(patientId: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/vital-signs/patient/${patientId}`, { headers });
  }

  async updateVitalSigns(id: string, vitalSignsData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.put(`${this.baseUrl}/vital-signs/${id}`, vitalSignsData, { headers });
  }

  async deleteVitalSigns(id: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.delete(`${this.baseUrl}/vital-signs/${id}`, { headers });
  }

  // Lab Results API
  async createLabResults(labResultsData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.post(`${this.baseUrl}/lab-results`, labResultsData, { headers });
  }

  async getLabResultsByPatient(patientId: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/lab-results/patient/${patientId}`, { headers });
  }

  async updateLabResults(id: string, labResultsData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.put(`${this.baseUrl}/lab-results/${id}`, labResultsData, { headers });
  }

  async deleteLabResults(id: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.delete(`${this.baseUrl}/lab-results/${id}`, { headers });
  }

  // Medication API
  async createMedication(medicationData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.post(`${this.baseUrl}/medications`, medicationData, { headers });
  }

  async getMedicationsByPatient(patientId: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/medications/patient/${patientId}`, { headers });
  }

  async updateMedication(id: string, medicationData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.put(`${this.baseUrl}/medications/${id}`, medicationData, { headers });
  }

  async deleteMedication(id: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.delete(`${this.baseUrl}/medications/${id}`, { headers });
  }

  // Medical Record API
  async createMedicalRecord(recordData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.post(`${this.baseUrl}/medical-records`, recordData, { headers });
  }

  async getMedicalRecordsByPatient(patientId: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.get(`${this.baseUrl}/medical-records/patient/${patientId}`, { headers });
  }

  async updateMedicalRecord(id: string, recordData: any): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.put(`${this.baseUrl}/medical-records/${id}`, recordData, { headers });
  }

  async deleteMedicalRecord(id: string): Promise<Observable<any>> {
    const headers = await this.getAuthHeaders();
    return this.http.delete(`${this.baseUrl}/medical-records/${id}`, { headers });
  }
}
