import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Preferences } from '@capacitor/preferences';
import { ApiService } from './api.service';

export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  // Add other user properties as needed
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private userSubject = new BehaviorSubject<User | null>(null);
  private tokenSubject = new BehaviorSubject<string | null>(null);
  private loadingSubject = new BehaviorSubject<boolean>(true);

  public user$ = this.userSubject.asObservable();
  public token$ = this.tokenSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();

  constructor(
    private apiService: ApiService
  ) {
    this.checkAuthState();
  }

  async checkAuthState(): Promise<void> {
    try {
      const { value: storedToken } = await Preferences.get({ key: 'token' });
      const { value: storedUser } = await Preferences.get({ key: 'user' });

      if (storedToken && storedUser) {
        this.tokenSubject.next(storedToken);
        this.userSubject.next(JSON.parse(storedUser));
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    } finally {
      this.loadingSubject.next(false);
    }
  }

  async login(username: string, password: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await (await this.apiService.login(username, password)).toPromise();
      const { token: authToken, ...userData } = response;

      await Preferences.set({ key: 'token', value: authToken });
      await Preferences.set({ key: 'user', value: JSON.stringify(userData) });

      this.tokenSubject.next(authToken);
      this.userSubject.next(userData);

      return { success: true };
    } catch (error: any) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.error?.message || 'Login failed'
      };
    }
  }

  async signup(userData: any): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const response = await (await this.apiService.signup(userData)).toPromise();
      return { success: true, message: response.message };
    } catch (error: any) {
      console.error('Signup error:', error);
      return {
        success: false,
        error: error.error?.message || 'Signup failed'
      };
    }
  }

  async logout(): Promise<void> {
    try {
      await Preferences.remove({ key: 'token' });
      await Preferences.remove({ key: 'user' });
      this.tokenSubject.next(null);
      this.userSubject.next(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  async refreshToken(): Promise<string | null> {
    try {
      const { value: storedToken } = await Preferences.get({ key: 'token' });
      if (storedToken) {
        const response = await (await this.apiService.refreshToken()).toPromise();
        const newToken = response.message.split(': ')[1]; // Extract token from message
        await Preferences.set({ key: 'token', value: newToken });
        this.tokenSubject.next(newToken);
        return newToken;
      }
      return null;
    } catch (error) {
      console.error('Token refresh error:', error);
      await this.logout(); // If refresh fails, logout user
      return null;
    }
  }

  getCurrentUser(): User | null {
    return this.userSubject.value;
  }

  getCurrentToken(): string | null {
    return this.tokenSubject.value;
  }

  isAuthenticated(): boolean {
    return !!this.tokenSubject.value;
  }
}
