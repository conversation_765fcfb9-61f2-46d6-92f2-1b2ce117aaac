/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";

/* Custom Global Styles */
:root {
  /* Primary Color Palette */
  --ion-color-primary: #667eea;
  --ion-color-primary-rgb: 102, 126, 234;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #5a6fd8;
  --ion-color-primary-tint: #7589ec;

  /* Secondary Color Palette */
  --ion-color-secondary: #764ba2;
  --ion-color-secondary-rgb: 118, 75, 162;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #68428f;
  --ion-color-secondary-tint: #8459ab;

  /* Custom Health Colors */
  --health-optimal: #10dc60;
  --health-normal: #3dc2ff;
  --health-warning: #ffce00;
  --health-danger: #f53d3d;
  --health-critical: #eb445a;

  /* Background Colors */
  --ion-background-color: #fafbfc;
  --ion-background-color-rgb: 250, 251, 252;

  /* Card Styles */
  --card-background: #ffffff;
  --card-border-radius: 16px;
  --card-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  --card-padding: 20px;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --heading-font-weight: 600;
  --body-font-weight: 400;
}

/* Global Typography */
* {
  font-family: var(--font-family);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: var(--heading-font-weight);
  color: var(--ion-color-dark);
}

/* Global Card Styles */
ion-card {
  border-radius: var(--card-border-radius) !important;
  box-shadow: var(--card-shadow) !important;
  border: none !important;
  background: var(--card-background) !important;
  margin: 16px !important;
  overflow: hidden !important;
}

ion-card-header {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  color: white;
  padding: var(--card-padding);
}

ion-card-title {
  color: white !important;
  font-weight: var(--heading-font-weight);
  font-size: 1.2rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;

  ion-icon {
    color: white;
    font-size: 1.4rem;
  }
}

ion-card-content {
  padding: var(--card-padding) !important;
}

/* Global Button Styles */
ion-button {
  --border-radius: 12px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.5px;

  &.button-large {
    height: 56px;
    font-size: 1.1rem;
    --border-radius: 16px;
  }

  &.primary-gradient {
    --background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
    --background-activated: linear-gradient(135deg, var(--ion-color-primary-shade) 0%, #68428f 100%);
    --color: white;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  }
}

/* Global Item Styles */
ion-item {
  --background: transparent;
  --border-color: transparent;
  --inner-border-width: 0;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 16px;

  ion-label {
    h3 {
      color: var(--ion-color-dark);
      font-weight: 600;
      margin-bottom: 4px;
      font-size: 0.95rem;
    }

    p {
      color: var(--ion-color-medium);
      margin: 0;
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }
}

/* Global Input Styles */
ion-input, ion-select, ion-textarea, ion-datetime {
  --background: white;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  border: 2px solid var(--ion-color-light-shade);
  border-radius: 12px;
  margin-top: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: var(--ion-color-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

/* Health Status Indicators */
.health-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;

  &.optimal {
    background: rgba(16, 220, 96, 0.1);
    color: var(--health-optimal);
  }

  &.normal {
    background: rgba(61, 194, 255, 0.1);
    color: var(--health-normal);
  }

  &.warning {
    background: rgba(255, 206, 0, 0.1);
    color: var(--health-warning);
  }

  &.danger {
    background: rgba(245, 61, 61, 0.1);
    color: var(--health-danger);
  }

  &.critical {
    background: rgba(235, 68, 90, 0.1);
    color: var(--health-critical);
  }
}

/* BMI Progress Bar */
.bmi-progress {
  margin: 16px 0;

  .bmi-scale {
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right,
      #3dc2ff 0%, #3dc2ff 18.5%,
      #10dc60 18.5%, #10dc60 25%,
      #ffce00 25%, #ffce00 30%,
      #f53d3d 30%, #f53d3d 100%);
    position: relative;
    margin: 8px 0;

    .bmi-indicator {
      position: absolute;
      top: -4px;
      width: 16px;
      height: 16px;
      background: white;
      border: 3px solid var(--ion-color-dark);
      border-radius: 50%;
      transform: translateX(-50%);
    }
  }

  .bmi-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--ion-color-medium);
    margin-top: 4px;
  }
}

/* Blood Pressure Indicator */
.bp-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 12px;
  font-weight: 600;

  &.optimal {
    background: rgba(16, 220, 96, 0.1);
    color: var(--health-optimal);
  }

  &.normal {
    background: rgba(61, 194, 255, 0.1);
    color: var(--health-normal);
  }

  &.high-normal {
    background: rgba(255, 206, 0, 0.1);
    color: var(--health-warning);
  }

  &.grade1-hypertension {
    background: rgba(245, 61, 61, 0.1);
    color: var(--health-danger);
  }

  &.grade2-hypertension {
    background: rgba(235, 68, 90, 0.1);
    color: var(--health-critical);
  }
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;

  ion-spinner {
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
    margin: 0;
    text-align: center;
  }
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);

  ion-icon {
    font-size: 4rem;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 8px 0;
    color: var(--ion-color-dark);
  }

  p {
    margin: 0;
    font-size: 0.9rem;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  ion-card {
    margin: 12px 8px !important;
  }

  ion-card-content {
    padding: 16px !important;
  }

  .bmi-labels {
    font-size: 0.7rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --ion-background-color: #121212;
    --card-background: #1e1e1e;
    --card-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  }

  ion-input, ion-select, ion-textarea, ion-datetime {
    --background: #2c2c2c;
    border-color: #404040;
    color: #ffffff;
  }
}
