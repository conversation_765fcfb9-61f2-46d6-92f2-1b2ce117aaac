/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";

/* Custom Global Styles - Light & Bright Theme */
:root {
  /* Primary Color Palette - Bright Blue */
  --ion-color-primary: #3498db;
  --ion-color-primary-rgb: 52, 152, 219;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #2980b9;
  --ion-color-primary-tint: #5dade2;

  /* Secondary Color Palette - Fresh Green */
  --ion-color-secondary: #2ecc71;
  --ion-color-secondary-rgb: 46, 204, 113;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #27ae60;
  --ion-color-secondary-tint: #58d68d;

  /* Custom Health Colors */
  --health-optimal: #2ecc71;
  --health-normal: #3498db;
  --health-warning: #f39c12;
  --health-danger: #e74c3c;
  --health-critical: #c0392b;

  /* Light Background Colors */
  --ion-background-color: #ffffff;
  --ion-background-color-rgb: 255, 255, 255;

  /* Card Styles - Clean & Bright */
  --card-background: #ffffff;
  --card-border-radius: 12px;
  --card-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  --card-padding: 20px;
  --card-border: 1px solid #f1f2f6;

  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --heading-font-weight: 600;
  --body-font-weight: 400;

  /* Text Colors - Dark on Light */
  --ion-color-dark: #2c3e50;
  --ion-color-medium: #7f8c8d;
  --ion-color-light: #ecf0f1;
  --ion-color-light-shade: #d5dbdb;
}

/* Global Typography */
* {
  font-family: var(--font-family);
}

body {
  background: var(--ion-background-color) !important;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: var(--heading-font-weight);
  color: var(--ion-color-dark);
}

/* Full Screen Layout */
ion-app {
  background: var(--ion-background-color) !important;
}

ion-content {
  --background: var(--ion-background-color) !important;
  --color: var(--ion-color-dark) !important;
}

/* Global Card Styles - Clean & Bright */
ion-card {
  border-radius: var(--card-border-radius) !important;
  box-shadow: var(--card-shadow) !important;
  border: var(--card-border) !important;
  background: var(--card-background) !important;
  margin: 12px !important;
  overflow: hidden !important;
}

ion-card-header {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  color: white;
  padding: var(--card-padding);
  border-bottom: none;
}

ion-card-title {
  color: white !important;
  font-weight: var(--heading-font-weight);
  font-size: 1.1rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;

  ion-icon {
    color: white;
    font-size: 1.2rem;
  }
}

ion-card-content {
  padding: var(--card-padding) !important;
  background: white !important;
}

/* Global Button Styles - Bright & Clean */
ion-button {
  --border-radius: 8px;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.3px;
  height: 44px;

  &.button-large {
    height: 52px;
    font-size: 1rem;
    --border-radius: 10px;
  }

  &.primary-gradient {
    --background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
    --background-activated: linear-gradient(135deg, var(--ion-color-primary-shade) 0%, var(--ion-color-secondary-shade) 100%);
    --color: white;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
  }
}

/* Global Item Styles - Clean & Spacious */
ion-item {
  --background: white;
  --border-color: transparent;
  --inner-border-width: 0;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  border: 1px solid #f1f2f6;

  ion-label {
    h3 {
      color: var(--ion-color-dark);
      font-weight: 600;
      margin-bottom: 6px;
      font-size: 1rem;
    }

    p {
      color: var(--ion-color-medium);
      margin: 0;
      font-size: 0.9rem;
      line-height: 1.5;
    }
  }
}

/* Global Input Styles - Clean & Bright */
ion-input, ion-select, ion-textarea, ion-datetime {
  --background: #fafbfc;
  --border-radius: 8px;
  --padding-start: 14px;
  --padding-end: 14px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --color: var(--ion-color-dark);
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  margin-top: 6px;
  font-size: 0.95rem;
  transition: all 0.2s ease;

  &:focus-within {
    border-color: var(--ion-color-primary);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
    --background: white;
  }
}

/* Health Status Indicators */
.health-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;

  &.optimal {
    background: rgba(16, 220, 96, 0.1);
    color: var(--health-optimal);
  }

  &.normal {
    background: rgba(61, 194, 255, 0.1);
    color: var(--health-normal);
  }

  &.warning {
    background: rgba(255, 206, 0, 0.1);
    color: var(--health-warning);
  }

  &.danger {
    background: rgba(245, 61, 61, 0.1);
    color: var(--health-danger);
  }

  &.critical {
    background: rgba(235, 68, 90, 0.1);
    color: var(--health-critical);
  }
}

/* BMI Progress Bar */
.bmi-progress {
  margin: 16px 0;

  .bmi-scale {
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right,
      #3dc2ff 0%, #3dc2ff 18.5%,
      #10dc60 18.5%, #10dc60 25%,
      #ffce00 25%, #ffce00 30%,
      #f53d3d 30%, #f53d3d 100%);
    position: relative;
    margin: 8px 0;

    .bmi-indicator {
      position: absolute;
      top: -4px;
      width: 16px;
      height: 16px;
      background: white;
      border: 3px solid var(--ion-color-dark);
      border-radius: 50%;
      transform: translateX(-50%);
    }
  }

  .bmi-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--ion-color-medium);
    margin-top: 4px;
  }
}

/* Blood Pressure Indicator */
.bp-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 12px;
  font-weight: 600;

  &.optimal {
    background: rgba(16, 220, 96, 0.1);
    color: var(--health-optimal);
  }

  &.normal {
    background: rgba(61, 194, 255, 0.1);
    color: var(--health-normal);
  }

  &.high-normal {
    background: rgba(255, 206, 0, 0.1);
    color: var(--health-warning);
  }

  &.grade1-hypertension {
    background: rgba(245, 61, 61, 0.1);
    color: var(--health-danger);
  }

  &.grade2-hypertension {
    background: rgba(235, 68, 90, 0.1);
    color: var(--health-critical);
  }
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;

  ion-spinner {
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
    margin: 0;
    text-align: center;
  }
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ion-color-medium);

  ion-icon {
    font-size: 4rem;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 8px 0;
    color: var(--ion-color-dark);
  }

  p {
    margin: 0;
    font-size: 0.9rem;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  ion-card {
    margin: 12px 8px !important;
  }

  ion-card-content {
    padding: 16px !important;
  }

  .bmi-labels {
    font-size: 0.7rem;
  }
}

/* Force Light Mode - Override Dark Mode */
@media (prefers-color-scheme: dark) {
  :root {
    --ion-background-color: #ffffff !important;
    --card-background: #ffffff !important;
    --card-shadow: 0 2px 12px rgba(0, 0, 0, 0.05) !important;
    --ion-color-dark: #2c3e50 !important;
    --ion-color-medium: #7f8c8d !important;
  }

  body {
    background: #ffffff !important;
    color: #2c3e50 !important;
  }

  ion-app {
    background: #ffffff !important;
  }

  ion-content {
    --background: #ffffff !important;
    --color: #2c3e50 !important;
  }

  ion-input, ion-select, ion-textarea, ion-datetime {
    --background: #fafbfc !important;
    border-color: #e1e5e9 !important;
    --color: #2c3e50 !important;
  }
}
